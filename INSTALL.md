# ZG Mining System - Installation Guide

This guide will walk you through installing the ZG Mining System on your FiveM server.

## Prerequisites

### Required Dependencies
- **oxmysql** - Database connector
- **FiveM Server** - Build 2802 or higher

### Optional Dependencies
- **ox_target** / **qb-target** / **qtarget** - For interaction system
- **ox_inventory** / **qb-inventory** / **qs-inventory** - For inventory integration
- **progressbar** / **qb-skillbar** - For progress bars and skill checks

## Step 1: Download and Extract

1. Download the ZG Mining System
2. Extract to your server's `resources` folder
3. Rename the folder to `ZGMining` (if needed)

## Step 2: Database Setup

The system will automatically create required tables on first start. Ensure your database user has CREATE TABLE permissions.

### Manual Table Creation (Optional)
If you prefer to create tables manually, run these SQL commands:

```sql
CREATE TABLE IF NOT EXISTS `players_mining` (
    `identifier` VARCHAR(50) NOT NULL,
    `xp` INT(11) NOT NULL DEFAULT 0,
    `level` INT(11) NOT NULL DEFAULT 1,
    `ores_mined` INT(11) NOT NULL DEFAULT 0,
    `time_mined` INT(11) NOT NULL DEFAULT 0,
    `money_earned` INT(11) NOT NULL DEFAULT 0,
    `items_smelted` INT(11) NOT NULL DEFAULT 0,
    `pickaxes_broken` INT(11) NOT NULL DEFAULT 0,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`identifier`)
);

CREATE TABLE IF NOT EXISTS `mining_grids` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(100) NOT NULL,
    `data` LONGTEXT NOT NULL,
    `created_by` VARCHAR(50) NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `active` TINYINT(1) DEFAULT 1,
    PRIMARY KEY (`id`)
);

CREATE TABLE IF NOT EXISTS `mining_smelt_jobs` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `owner` VARCHAR(50) NOT NULL,
    `recipe_id` VARCHAR(50) NOT NULL,
    `quantity` INT(11) NOT NULL DEFAULT 1,
    `started_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `eta` TIMESTAMP NOT NULL,
    `status` ENUM('queued', 'active', 'completed', 'cancelled') DEFAULT 'queued',
    `furnace_id` INT(11) DEFAULT NULL,
    `data` TEXT,
    PRIMARY KEY (`id`)
);

CREATE TABLE IF NOT EXISTS `mining_price_history` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `item` VARCHAR(50) NOT NULL,
    `price` DECIMAL(10,2) NOT NULL,
    `quantity_sold` INT(11) NOT NULL DEFAULT 0,
    `timestamp` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
);
```

## Step 3: Framework-Specific Setup

### QBCore Setup

1. **Add items to qb-core/shared/items.lua:**
```lua
-- Pickaxes
['pickaxe_wood'] = {['name'] = 'pickaxe_wood', ['label'] = 'Wooden Pickaxe', ['weight'] = 2000, ['type'] = 'item', ['image'] = 'pickaxe_wood.png', ['unique'] = true, ['useable'] = true, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'A basic wooden pickaxe for mining'},
['pickaxe_stone'] = {['name'] = 'pickaxe_stone', ['label'] = 'Stone Pickaxe', ['weight'] = 2500, ['type'] = 'item', ['image'] = 'pickaxe_stone.png', ['unique'] = true, ['useable'] = true, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'A sturdy stone pickaxe'},
['pickaxe_iron'] = {['name'] = 'pickaxe_iron', ['label'] = 'Iron Pickaxe', ['weight'] = 3000, ['type'] = 'item', ['image'] = 'pickaxe_iron.png', ['unique'] = true, ['useable'] = true, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'A reliable iron pickaxe'},
['pickaxe_steel'] = {['name'] = 'pickaxe_steel', ['label'] = 'Steel Pickaxe', ['weight'] = 3500, ['type'] = 'item', ['image'] = 'pickaxe_steel.png', ['unique'] = true, ['useable'] = true, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'A high-quality steel pickaxe'},
['pickaxe_titanium'] = {['name'] = 'pickaxe_titanium', ['label'] = 'Titanium Pickaxe', ['weight'] = 4000, ['type'] = 'item', ['image'] = 'pickaxe_titanium.png', ['unique'] = true, ['useable'] = true, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'An advanced titanium pickaxe'},
['pickaxe_diamond'] = {['name'] = 'pickaxe_diamond', ['label'] = 'Diamond Pickaxe', ['weight'] = 4500, ['type'] = 'item', ['image'] = 'pickaxe_diamond.png', ['unique'] = true, ['useable'] = true, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'The ultimate diamond-tipped pickaxe'},
['pickaxe_repairkit'] = {['name'] = 'pickaxe_repairkit', ['label'] = 'Pickaxe Repair Kit', ['weight'] = 500, ['type'] = 'item', ['image'] = 'repair_kit.png', ['unique'] = false, ['useable'] = true, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'Repairs pickaxe durability'},

-- Ores
['ore_coal'] = {['name'] = 'ore_coal', ['label'] = 'Coal Ore', ['weight'] = 800, ['type'] = 'item', ['image'] = 'ore_coal.png', ['unique'] = false, ['useable'] = false, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'Raw coal ore, used for fuel'},
['ore_copper'] = {['name'] = 'ore_copper', ['label'] = 'Copper Ore', ['weight'] = 1000, ['type'] = 'item', ['image'] = 'ore_copper.png', ['unique'] = false, ['useable'] = false, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'Raw copper ore'},
['ore_iron'] = {['name'] = 'ore_iron', ['label'] = 'Iron Ore', ['weight'] = 1200, ['type'] = 'item', ['image'] = 'ore_iron.png', ['unique'] = false, ['useable'] = false, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'Raw iron ore'},
['ore_silver'] = {['name'] = 'ore_silver', ['label'] = 'Silver Ore', ['weight'] = 1500, ['type'] = 'item', ['image'] = 'ore_silver.png', ['unique'] = false, ['useable'] = false, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'Raw silver ore'},
['ore_gold'] = {['name'] = 'ore_gold', ['label'] = 'Gold Ore', ['weight'] = 2000, ['type'] = 'item', ['image'] = 'ore_gold.png', ['unique'] = false, ['useable'] = false, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'Raw gold ore'},
['ore_titanium'] = {['name'] = 'ore_titanium', ['label'] = 'Titanium Ore', ['weight'] = 2500, ['type'] = 'item', ['image'] = 'ore_titanium.png', ['unique'] = false, ['useable'] = false, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'Raw titanium ore'},
['ore_diamondShard'] = {['name'] = 'ore_diamondShard', ['label'] = 'Diamond Shard', ['weight'] = 100, ['type'] = 'item', ['image'] = 'ore_diamond.png', ['unique'] = false, ['useable'] = false, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'A raw diamond shard'},

-- Ingots
['ingot_copper'] = {['name'] = 'ingot_copper', ['label'] = 'Copper Ingot', ['weight'] = 1000, ['type'] = 'item', ['image'] = 'ingot_copper.png', ['unique'] = false, ['useable'] = false, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'Refined copper ingot'},
['ingot_iron'] = {['name'] = 'ingot_iron', ['label'] = 'Iron Ingot', ['weight'] = 1200, ['type'] = 'item', ['image'] = 'ingot_iron.png', ['unique'] = false, ['useable'] = false, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'Refined iron ingot'},
['ingot_silver'] = {['name'] = 'ingot_silver', ['label'] = 'Silver Ingot', ['weight'] = 1500, ['type'] = 'item', ['image'] = 'ingot_silver.png', ['unique'] = false, ['useable'] = false, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'Refined silver ingot'},
['ingot_gold'] = {['name'] = 'ingot_gold', ['label'] = 'Gold Ingot', ['weight'] = 2000, ['type'] = 'item', ['image'] = 'ingot_gold.png', ['unique'] = false, ['useable'] = false, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'Refined gold ingot'},
['ingot_titanium'] = {['name'] = 'ingot_titanium', ['label'] = 'Titanium Ingot', ['weight'] = 2500, ['type'] = 'item', ['image'] = 'ingot_titanium.png', ['unique'] = false, ['useable'] = false, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'Refined titanium ingot'},
['gem_diamond'] = {['name'] = 'gem_diamond', ['label'] = 'Cut Diamond', ['weight'] = 100, ['type'] = 'item', ['image'] = 'gem_diamond.png', ['unique'] = false, ['useable'] = false, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'A perfectly cut diamond'},
['coal_coke'] = {['name'] = 'coal_coke', ['label'] = 'Coal Coke', ['weight'] = 500, ['type'] = 'item', ['image'] = 'coal_coke.png', ['unique'] = false, ['useable'] = false, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'High-grade fuel for smelting'},
```

### ESX Setup

1. **Add items to es_extended/config.lua or your items database:**
```sql
INSERT INTO `items` (`name`, `label`, `weight`, `rare`, `can_remove`) VALUES
('pickaxe_wood', 'Wooden Pickaxe', 2, 0, 1),
('pickaxe_stone', 'Stone Pickaxe', 3, 0, 1),
('pickaxe_iron', 'Iron Pickaxe', 3, 0, 1),
('pickaxe_steel', 'Steel Pickaxe', 4, 0, 1),
('pickaxe_titanium', 'Titanium Pickaxe', 4, 0, 1),
('pickaxe_diamond', 'Diamond Pickaxe', 5, 0, 1),
('pickaxe_repairkit', 'Pickaxe Repair Kit', 1, 0, 1),
('ore_coal', 'Coal Ore', 1, 0, 1),
('ore_copper', 'Copper Ore', 1, 0, 1),
('ore_iron', 'Iron Ore', 1, 0, 1),
('ore_silver', 'Silver Ore', 2, 0, 1),
('ore_gold', 'Gold Ore', 2, 0, 1),
('ore_titanium', 'Titanium Ore', 3, 0, 1),
('ore_diamondShard', 'Diamond Shard', 0, 0, 1),
('ingot_copper', 'Copper Ingot', 1, 0, 1),
('ingot_iron', 'Iron Ingot', 1, 0, 1),
('ingot_silver', 'Silver Ingot', 2, 0, 1),
('ingot_gold', 'Gold Ingot', 2, 0, 1),
('ingot_titanium', 'Titanium Ingot', 3, 0, 1),
('gem_diamond', 'Cut Diamond', 0, 0, 1),
('coal_coke', 'Coal Coke', 1, 0, 1);
```

### QBOX Setup

QBOX uses the same item format as QBCore. Follow the QBCore setup instructions above.

## Step 4: Server Configuration

1. **Add to server.cfg:**
```cfg
ensure oxmysql
ensure ZGMining
```

2. **Set permissions (optional):**
```cfg
# Admin permissions
add_ace group.admin mining.admin allow
add_ace group.admin mining.place allow
```

## Step 5: Resource Configuration

1. **Edit config/config.lua** to match your server setup
2. **Adjust PED locations** if needed (already set to specified coordinates)
3. **Configure economy settings** for your server's balance

## Step 6: Start the Resource

1. Start your server
2. The resource will automatically:
   - Create database tables
   - Spawn NPCs at configured locations
   - Register all items with your framework

## Step 7: Testing

1. **Test basic functionality:**
   - Use `/miningmenu` to open the interface
   - Visit the NPCs to test interactions
   - Try mining if grids are placed

2. **Test admin features:**
   - Use `/miningplace` to test 3D placement
   - Place a test mining grid
   - Verify grid persistence after restart

## Troubleshooting

### Common Issues

1. **Resource won't start:**
   - Check console for errors
   - Ensure oxmysql is running
   - Verify file permissions

2. **Items not working:**
   - Ensure items are added to your framework
   - Check item names match exactly
   - Restart your inventory resource

3. **NPCs not spawning:**
   - Check coordinates in config
   - Verify no conflicting resources
   - Check for script errors

4. **Database errors:**
   - Verify MySQL connection
   - Check user permissions
   - Ensure database exists

### Getting Help

If you encounter issues:
1. Check the console for error messages
2. Enable debug mode in config.lua
3. Review the README.md for additional information
4. Contact support with specific error messages

## Post-Installation

### Recommended Settings

1. **Performance optimization:**
   - Adjust `Config.Performance.cullDistance` based on server population
   - Set `Config.Rocks.maxConcurrent` appropriately

2. **Economy balancing:**
   - Monitor sell prices and adjust as needed
   - Configure job bonuses for your server

3. **Admin setup:**
   - Train admins on 3D placement tools
   - Set up initial mining grids

### Maintenance

- **Regular backups** of the database
- **Monitor performance** with resmon
- **Update configurations** as needed
- **Check logs** for any issues

---

**Installation Complete!** Your ZG Mining System is now ready to use.
