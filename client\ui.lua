-- ZG Mining System - UI Management

local UIData = {
    playerData = {},
    sellPrices = {},
    smeltJobs = {},
    leaderboards = {},
    marketData = {}
}

-- Initialize UI system
CreateThread(function()
    -- Setup NUI message handlers
    SetupNUIHandlers()
end)

-- Setup NUI message handlers
function SetupNUIHandlers()
    -- Player data updates
    RegisterNetEvent('ZGMining:UpdatePlayerData', function(data)
        UIData.playerData = data
        
        SendNUIMessage({
            action = 'updatePlayerData',
            data = data
        })
    end)
    
    -- Sell prices
    RegisterNetEvent('ZGMining:ReceiveSellPrices', function(prices)
        UIData.sellPrices = prices
        
        SendNUIMessage({
            action = 'updateSellPrices',
            data = prices
        })
    end)
    
    -- Smelt jobs
    RegisterNetEvent('ZGMining:ReceiveSmeltJobs', function(jobs)
        UIData.smeltJobs = jobs
        
        SendNUIMessage({
            action = 'updateSmeltJobs',
            data = jobs
        })
    end)
    
    -- Leaderboards
    RegisterNetEvent('ZGMining:ReceiveLeaderboards', function(leaderboards)
        UIData.leaderboards = leaderboards
        
        SendNUIMessage({
            action = 'updateLeaderboards',
            data = leaderboards
        })
    end)
    
    -- Market data
    RegisterNetEvent('ZGMining:ReceiveMarketData', function(marketData)
        UIData.marketData = marketData
        
        SendNUIMessage({
            action = 'updateMarketData',
            data = marketData
        })
    end)
end

-- Send notification to UI
function SendUINotification(message, type, duration)
    SendNUIMessage({
        action = 'notification',
        data = {
            message = message,
            type = type or 'info',
            duration = duration or 5000
        }
    })
end

-- Update progress bar
function UpdateProgressBar(id, progress, label)
    SendNUIMessage({
        action = 'updateProgress',
        data = {
            id = id,
            progress = progress,
            label = label
        }
    })
end

-- Show skill check UI
function ShowSkillCheck(difficulty, callback)
    SendNUIMessage({
        action = 'showSkillCheck',
        data = {
            difficulty = difficulty
        }
    })
    
    -- Store callback for result
    _G.SkillCheckCallback = callback
end

-- NUI Callbacks
RegisterNUICallback('skillCheckResult', function(data, cb)
    if _G.SkillCheckCallback then
        _G.SkillCheckCallback(data.success)
        _G.SkillCheckCallback = nil
    end
    cb('ok')
end)

RegisterNUICallback('playSound', function(data, cb)
    if data.sound and data.soundSet then
        PlaySoundFrontend(-1, data.sound, data.soundSet, false)
    end
    cb('ok')
end)

RegisterNUICallback('requestInventory', function(data, cb)
    -- Get player inventory (this would integrate with the inventory system)
    local inventory = GetPlayerInventory()
    cb(inventory)
end)

RegisterNUICallback('requestShopItems', function(data, cb)
    -- Get shop items
    local shopItems = GetShopItems()
    cb(shopItems)
end)

-- Get player inventory
function GetPlayerInventory()
    local inventory = {}
    
    -- This would integrate with the actual inventory system
    -- For now, return sample data
    for itemName, itemData in pairs(Items.All) do
        if string.find(itemName, 'ore_') or string.find(itemName, 'ingot_') then
            table.insert(inventory, {
                name = itemName,
                label = itemData.label,
                count = math.random(0, 10), -- Sample count
                image = itemData.image,
                sellable = Config.Economy.basePrices[itemName] ~= nil
            })
        end
    end
    
    return inventory
end

-- Get shop items
function GetShopItems()
    local shopItems = {}
    
    for itemName, itemData in pairs(Items.Tools) do
        table.insert(shopItems, {
            name = itemName,
            label = itemData.label,
            description = itemData.description,
            price = itemData.price,
            image = itemData.image,
            tier = itemData.tier,
            levelRequired = itemData.levelRequired,
            stats = {
                damage = itemData.damage,
                durability = itemData.durabilityMax,
                swingRate = itemData.swingRate,
                critChance = itemData.critChance
            }
        })
    end
    
    return shopItems
end

-- Animation and effect helpers
function PlayUIAnimation(animationType, duration)
    SendNUIMessage({
        action = 'playAnimation',
        data = {
            type = animationType,
            duration = duration or 1000
        }
    })
end

function ShowUIEffect(effectType, data)
    SendNUIMessage({
        action = 'showEffect',
        data = {
            type = effectType,
            data = data
        }
    })
end

-- Level up effect
RegisterNetEvent('ZGMining:LevelUp', function(newLevel)
    ShowUIEffect('levelUp', {
        level = newLevel,
        message = Utils.Locale('level_up', newLevel)
    })
    
    PlayUIAnimation('celebration', 3000)
end)

-- XP gained effect
RegisterNetEvent('ZGMining:XPGained', function(amount, totalXP, level)
    ShowUIEffect('xpGain', {
        amount = amount,
        totalXP = totalXP,
        level = level
    })
end)

-- Smelting events
RegisterNetEvent('ZGMining:SmeltStarted', function(job)
    SendUINotification(Utils.Locale('smelting_queue'), 'success')
    
    -- Update smelt jobs display
    TriggerServerEvent('ZGMining:GetSmeltJobs')
end)

RegisterNetEvent('ZGMining:SmeltCompleted', function(jobId)
    SendUINotification(Utils.Locale('smelting_complete'), 'success')
    
    ShowUIEffect('smeltComplete', {
        jobId = jobId
    })
    
    -- Update smelt jobs display
    TriggerServerEvent('ZGMining:GetSmeltJobs')
end)

RegisterNetEvent('ZGMining:SmeltCancelled', function(jobId)
    SendUINotification(Utils.Locale('smelt_cancelled'), 'info')
    
    -- Update smelt jobs display
    TriggerServerEvent('ZGMining:GetSmeltJobs')
end)

-- Selling events
RegisterNetEvent('ZGMining:SellComplete', function(sellData)
    ShowUIEffect('sellComplete', {
        total = sellData.total,
        items = sellData.items
    })
    
    SendUINotification(
        Utils.Locale('sale_successful') .. ' ' .. Utils.FormatMoney(sellData.total),
        'success'
    )
end)

-- Mining events
RegisterNetEvent('ZGMining:MiningStarted', function(rockId, pickaxeData)
    SendUINotification(Utils.Locale('mining_in_progress'), 'info')
    
    -- Show mining progress UI
    SendNUIMessage({
        action = 'showMiningProgress',
        data = {
            rockId = rockId,
            pickaxe = pickaxeData
        }
    })
end)

RegisterNetEvent('ZGMining:MiningStopped', function()
    SendNUIMessage({
        action = 'hideMiningProgress'
    })
end)

RegisterNetEvent('ZGMining:MiningHit', function(newHealth, maxHealth)
    local progress = ((maxHealth - newHealth) / maxHealth) * 100
    
    UpdateProgressBar('mining', progress, Utils.Locale('mining_in_progress'))
end)

-- Tutorial system
function ShowTutorial(step)
    SendNUIMessage({
        action = 'showTutorial',
        data = {
            step = step,
            message = Utils.Locale('tutorial_step' .. step)
        }
    })
end

-- First time player tutorial
CreateThread(function()
    Wait(5000) -- Wait for everything to load
    
    if UIData.playerData.level == 1 and UIData.playerData.xp == 0 then
        ShowTutorial(1)
    end
end)

-- Keybind helpers
function RegisterUIKeybinds()
    RegisterKeyMapping('mining_interact', Utils.Locale('interact'), 'keyboard', 'E')
    RegisterKeyMapping('mining_cancel', Utils.Locale('cancel_action'), 'keyboard', 'BACKSPACE')
    
    RegisterCommand('mining_interact', function()
        -- Handle interaction
        TriggerEvent('ZGMining:HandleInteraction')
    end, false)
    
    RegisterCommand('mining_cancel', function()
        -- Handle cancel
        TriggerEvent('ZGMining:HandleCancel')
    end, false)
end

-- Initialize keybinds
CreateThread(function()
    RegisterUIKeybinds()
end)

-- Utility functions for UI
function FormatTimeRemaining(seconds)
    if seconds <= 0 then return '00:00' end
    
    local minutes = math.floor(seconds / 60)
    local secs = seconds % 60
    
    return string.format('%02d:%02d', minutes, secs)
end

function GetPlayerLevelProgress()
    local currentXP = UIData.playerData.xp or 0
    local level = UIData.playerData.level or 1
    
    local currentLevelXP = Utils.CalculateXPForLevel(level)
    local nextLevelXP = Utils.CalculateXPForLevel(level + 1)
    
    local progress = 0
    if nextLevelXP > currentLevelXP then
        progress = ((currentXP - currentLevelXP) / (nextLevelXP - currentLevelXP)) * 100
    end
    
    return {
        level = level,
        progress = math.max(0, math.min(100, progress)),
        currentXP = currentXP,
        nextLevelXP = nextLevelXP,
        remaining = nextLevelXP - currentXP
    }
end

-- Export UI functions
exports('SendUINotification', SendUINotification)
exports('UpdateProgressBar', UpdateProgressBar)
exports('ShowSkillCheck', ShowSkillCheck)
exports('GetPlayerLevelProgress', GetPlayerLevelProgress)
