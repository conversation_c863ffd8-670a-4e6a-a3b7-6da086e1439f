Config = {}

-- Framework Detection
Config.Framework = 'auto' -- 'auto', 'qbcore', 'esx', 'qbox'

-- Target System
Config.Target = {
    system = 'auto', -- 'auto', 'ox_target', 'qb-target', 'qtarget', 'text'
    distance = 2.5
}

-- Inventory System
Config.Inventory = {
    system = 'auto', -- 'auto', 'ox_inventory', 'qb-inventory', 'qs-inventory'
}

-- XP and Leveling System
Config.XP = {
    base = 100,
    scale = 1.35,
    maxLevel = 50,
    perks = {
        sellBonus = 0.05, -- +5% sell price per 10 levels (max 25%)
        doubleDropChance = 0.02, -- +2% double drop chance per 5 levels
        smeltTimeReduction = 0.05 -- -5% smelt time per 8 levels
    }
}

-- Pickaxe System
Config.Pickaxes = {
    durabilityLoss = {
        wood = 2.5,
        stone = 2.0,
        iron = 1.5,
        steel = 1.2,
        titanium = 0.9,
        diamond = 0.6
    },
    critFailChance = {
        wood = 0.02,
        stone = 0.015,
        iron = 0.01,
        steel = 0.008,
        titanium = 0.005,
        diamond = 0.002
    },
    critFailDamage = {
        min = 10,
        max = 25
    }
}

-- Rock System
Config.Rocks = {
    health = {
        copper = 60,
        iron = 90,
        silver = 130,
        gold = 180,
        titanium = 240,
        diamond = 300
    },
    respawnTime = 300, -- 5 minutes
    maxConcurrent = 50,
    dropRates = {
        ore = { min = 3, max = 7 },
        gem = { min = 0.01, max = 0.04 } -- 1-4% chance
    }
}

-- Grid Placement System
Config.Placement = {
    defaults = {
        rows = 5,
        cols = 5,
        spacing = 3.0,
        yawSnap = 15.0,
        jitter = 0.1,
        scale = 1.0,
        maxSpawns = 10,
        respawnTime = 300
    },
    ghostProp = `prop_rock_1_a`,
    maxDistance = 200.0,
    permissions = {
        place = 'mining.admin',
        manage = 'mining.admin'
    }
}

-- PED Locations
Config.Peds = {
    foreman = {
        coords = vector4(2944.4258, 2746.8281, 43.3645, 288.2603),
        model = `s_m_y_construct_01`,
        blip = {
            sprite = 566,
            color = 5,
            scale = 0.8,
            label = 'Mining Foreman'
        }
    },
    seller = {
        coords = vector4(1090.5807, -1999.9025, 30.9295, 136.3622),
        model = `s_m_m_jeweller_01`,
        blip = {
            sprite = 567,
            color = 2,
            scale = 0.8,
            label = 'Ore Buyer'
        }
    },
    smelter = {
        coords = vector4(1109.9464, -2008.1166, 31.0586, 58.9880),
        model = `s_m_y_factory_01`,
        blip = {
            sprite = 478,
            color = 6,
            scale = 0.8,
            label = 'Smelter'
        }
    }
}

-- Economy System
Config.Economy = {
    basePrices = {
        -- Ores
        ore_coal = 2,
        ore_copper = 3,
        ore_iron = 5,
        ore_silver = 10,
        ore_gold = 20,
        ore_titanium = 30,
        ore_diamondShard = 50,
        -- Ingots
        ingot_copper = 35,
        ingot_iron = 60,
        ingot_silver = 100,
        ingot_gold = 175,
        ingot_titanium = 260,
        gem_diamond = 600
    },
    dynamicPricing = {
        enabled = true,
        window = 24, -- hours
        maxFluctuation = 0.3 -- ±30%
    },
    tax = 0.05, -- 5% tax
    jobBonus = {
        enabled = true,
        jobs = { 'miner' },
        multiplier = 1.15
    }
}

-- Controls
Config.Controls = {
    openMenu = 166, -- F5
    interact = 38, -- E
    cancel = 177, -- BACKSPACE
    controller = {
        openMenu = 27, -- DPAD_RIGHT
        interact = 51, -- INPUT_CONTEXT
        cancel = 177
    }
}

-- Anti-Exploit
Config.AntiExploit = {
    maxDistance = 5.0,
    hitCooldown = 1000, -- ms
    maxHitsPerSecond = 2,
    combatLogProtection = true,
    inventoryChecks = true
}

-- Performance
Config.Performance = {
    updateInterval = 250, -- ms
    cullDistance = 100.0,
    maxGhostProps = 25
}

-- Locale
Config.Locale = 'en'
Config.AvailableLocales = { 'en', 'es', 'fr', 'de' }

-- Debug
Config.Debug = false
