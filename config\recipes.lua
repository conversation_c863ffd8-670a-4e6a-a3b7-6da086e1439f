Recipes = {}

-- Smelting Recipes
Recipes.Smelting = {
    copper_ingot = {
        id = 'copper_ingot',
        label = 'Copper Ingot',
        description = 'Smelt copper ore into ingots',
        inputs = {
            { item = 'ore_copper', count = 5 },
            { item = 'ore_coal', count = 5 }
        },
        outputs = {
            { item = 'ingot_copper', count = 1 }
        },
        time = 30, -- seconds
        xp = 25,
        heat = 800, -- temperature required
        levelRequired = 1,
        category = 'basic'
    },
    iron_ingot = {
        id = 'iron_ingot',
        label = 'Iron Ingot',
        description = 'Smelt iron ore into ingots',
        inputs = {
            { item = 'ore_iron', count = 5 },
            { item = 'ore_coal', count = 10 }
        },
        outputs = {
            { item = 'ingot_iron', count = 1 }
        },
        time = 40,
        xp = 35,
        heat = 1200,
        levelRequired = 5,
        category = 'basic'
    },
    silver_ingot = {
        id = 'silver_ingot',
        label = 'Silver Ingot',
        description = 'Smelt silver ore into ingots',
        inputs = {
            { item = 'ore_silver', count = 5 },
            { item = 'ore_coal', count = 15 }
        },
        outputs = {
            { item = 'ingot_silver', count = 1 }
        },
        time = 55,
        xp = 55,
        heat = 1500,
        levelRequired = 10,
        category = 'intermediate'
    },
    gold_ingot = {
        id = 'gold_ingot',
        label = 'Gold Ingot',
        description = 'Smelt gold ore into ingots',
        inputs = {
            { item = 'ore_gold', count = 5 },
            { item = 'ore_coal', count = 20 }
        },
        outputs = {
            { item = 'ingot_gold', count = 1 }
        },
        time = 75,
        xp = 80,
        heat = 1800,
        levelRequired = 20,
        category = 'advanced'
    },
    titanium_ingot = {
        id = 'titanium_ingot',
        label = 'Titanium Ingot',
        description = 'Smelt titanium ore into ingots',
        inputs = {
            { item = 'ore_titanium', count = 5 },
            { item = 'coal_coke', count = 25 }
        },
        outputs = {
            { item = 'ingot_titanium', count = 1 }
        },
        time = 95,
        xp = 110,
        heat = 2200,
        levelRequired = 30,
        category = 'expert'
    },
    diamond_gem = {
        id = 'diamond_gem',
        label = 'Cut Diamond',
        description = 'Cut and polish diamond shards',
        inputs = {
            { item = 'ore_diamondShard', count = 10 },
            { item = 'coal_coke', count = 15 }
        },
        outputs = {
            { item = 'gem_diamond', count = 1 }
        },
        time = 120,
        xp = 150,
        heat = 2500,
        levelRequired = 40,
        category = 'master'
    },
    coal_coke = {
        id = 'coal_coke',
        label = 'Coal Coke',
        description = 'Process coal into high-grade fuel',
        inputs = {
            { item = 'ore_coal', count = 10 }
        },
        outputs = {
            { item = 'coal_coke', count = 5 }
        },
        time = 20,
        xp = 15,
        heat = 600,
        levelRequired = 1,
        category = 'basic'
    }
}

-- Repair Recipes
Recipes.Repair = {
    pickaxe_repair = {
        id = 'pickaxe_repair',
        label = 'Pickaxe Repair',
        description = 'Repair pickaxe durability',
        inputs = {
            { item = 'pickaxe_repairkit', count = 1 }
        },
        restorePercent = 0.75, -- Restore 75% of max durability
        time = 5,
        xp = 5
    },
    pickaxe_repair_cash = {
        id = 'pickaxe_repair_cash',
        label = 'Pickaxe Repair (Cash)',
        description = 'Repair pickaxe with cash',
        cost = 50, -- Base cost, scales with tier
        restorePercent = 0.5, -- Restore 50% of max durability
        time = 3,
        xp = 2
    }
}

-- Crafting Categories
Recipes.Categories = {
    basic = {
        label = 'Basic Smelting',
        description = 'Basic ore processing',
        icon = 'fire',
        color = '#ff6b35'
    },
    intermediate = {
        label = 'Intermediate Smelting',
        description = 'Advanced ore processing',
        icon = 'flame',
        color = '#f7931e'
    },
    advanced = {
        label = 'Advanced Smelting',
        description = 'High-grade ore processing',
        icon = 'fire-flame-curved',
        color = '#ffcc02'
    },
    expert = {
        label = 'Expert Smelting',
        description = 'Expert-level processing',
        icon = 'fire-flame-simple',
        color = '#37b24d'
    },
    master = {
        label = 'Master Smelting',
        description = 'Master-level processing',
        icon = 'fire-burner',
        color = '#845ef7'
    }
}

-- Heat Sources
Recipes.HeatSources = {
    coal = {
        item = 'ore_coal',
        heat = 100,
        duration = 60 -- seconds
    },
    coal_coke = {
        item = 'coal_coke',
        heat = 200,
        duration = 120
    }
}

-- Recipe Utilities
function Recipes.GetRecipe(id)
    return Recipes.Smelting[id] or Recipes.Repair[id]
end

function Recipes.GetRecipesByCategory(category)
    local recipes = {}
    for id, recipe in pairs(Recipes.Smelting) do
        if recipe.category == category then
            recipes[id] = recipe
        end
    end
    return recipes
end

function Recipes.GetRecipesByLevel(level)
    local recipes = {}
    for id, recipe in pairs(Recipes.Smelting) do
        if recipe.levelRequired <= level then
            recipes[id] = recipe
        end
    end
    return recipes
end

function Recipes.CalculateSmeltTime(baseTime, level, bonuses)
    local timeReduction = 0
    
    -- Level bonus
    if level and Config.XP.perks.smeltTimeReduction then
        local levelBonus = math.floor(level / 8) * Config.XP.perks.smeltTimeReduction
        timeReduction = timeReduction + math.min(levelBonus, 0.5) -- Max 50% reduction
    end
    
    -- Additional bonuses
    if bonuses then
        for _, bonus in pairs(bonuses) do
            timeReduction = timeReduction + bonus
        end
    end
    
    return math.max(baseTime * (1 - timeReduction), baseTime * 0.1) -- Minimum 10% of original time
end
