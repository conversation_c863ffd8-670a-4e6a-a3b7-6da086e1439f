-- ZG Mining System - Smelting System

local SmeltJobs = {}
local SmeltQueue = {}
local ActiveSmelts = {}

-- Initialize smelting system
local function InitializeSmelting()
    -- Create smelting jobs table
    MySQL.query([[
        CREATE TABLE IF NOT EXISTS `mining_smelt_jobs` (
            `id` INT(11) NOT NULL AUTO_INCREMENT,
            `owner` VARCHAR(50) NOT NULL,
            `recipe_id` VARCHAR(50) NOT NULL,
            `quantity` INT(11) NOT NULL DEFAULT 1,
            `started_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            `eta` TIMESTAMP NOT NULL,
            `status` ENUM('queued', 'active', 'completed', 'cancelled') DEFAULT 'queued',
            `furnace_id` INT(11) DEFAULT NULL,
            `data` TEXT,
            PRIMARY KEY (`id`),
            INDEX `idx_owner_status` (`owner`, `status`),
            INDEX `idx_status_eta` (`status`, `eta`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ]])
    
    -- Load active smelt jobs
    LoadActiveSmeltJobs()
    
    Utils.Log('info', 'Smelting system initialized')
end

-- Load active smelt jobs from database
local function LoadActiveSmeltJobs()
    MySQL.query("SELECT * FROM mining_smelt_jobs WHERE status IN ('queued', 'active')", {}, function(results)
        if results then
            for _, job in pairs(results) do
                SmeltJobs[job.id] = {
                    id = job.id,
                    owner = job.owner,
                    recipeId = job.recipe_id,
                    quantity = job.quantity,
                    startedAt = job.started_at,
                    eta = job.eta,
                    status = job.status,
                    furnaceId = job.furnace_id,
                    data = job.data and json.decode(job.data) or {}
                }
                
                if job.status == 'active' then
                    ActiveSmelts[job.id] = SmeltJobs[job.id]
                end
            end
            
            Utils.Log('info', 'Loaded %d active smelt jobs', #results)
        end
    end)
end

-- Start smelting process
RegisterNetEvent('ZGMining:StartSmelting', function(recipeId, quantity)
    local source = source
    local playerData = exports['ZGMining']:GetPlayerMiningData(source)
    
    if not playerData then
        Framework.Notify(source, Utils.Locale('error_generic'), 'error')
        return
    end
    
    local recipe = Recipes.GetRecipe(recipeId)
    if not recipe then
        Framework.Notify(source, Utils.Locale('error_invalid_recipe'), 'error')
        return
    end
    
    quantity = quantity or 1
    
    -- Check level requirement
    if playerData.level < recipe.levelRequired then
        Framework.Notify(source, Utils.Locale('level_requirement', recipe.levelRequired), 'error')
        return
    end
    
    -- Check if player has required items
    for _, input in pairs(recipe.inputs) do
        local requiredAmount = input.count * quantity
        if not Framework.HasItem(source, input.item, requiredAmount) then
            local itemLabel = Items.All[input.item] and Items.All[input.item].label or input.item
            Framework.Notify(source, Utils.Locale('insufficient_items') .. ': ' .. itemLabel, 'error')
            return
        end
    end
    
    -- Check queue limit (max 3 concurrent smelts per player)
    local playerActiveJobs = 0
    for _, job in pairs(SmeltJobs) do
        if job.owner == Framework.GetPlayerIdentifier(source) and job.status ~= 'completed' and job.status ~= 'cancelled' then
            playerActiveJobs = playerActiveJobs + 1
        end
    end
    
    if playerActiveJobs >= 3 then
        Framework.Notify(source, Utils.Locale('furnace_busy'), 'warning')
        return
    end
    
    -- Remove required items
    for _, input in pairs(recipe.inputs) do
        local requiredAmount = input.count * quantity
        if not Framework.RemoveItem(source, input.item, requiredAmount) then
            -- Rollback previous removals
            Framework.Notify(source, Utils.Locale('error_generic'), 'error')
            return
        end
    end
    
    -- Calculate smelting time with bonuses
    local smeltTime = Recipes.CalculateSmeltTime(recipe.time, playerData.level)
    local eta = os.time() + (smeltTime * quantity)
    
    -- Create smelt job
    MySQL.insert([[
        INSERT INTO mining_smelt_jobs (owner, recipe_id, quantity, eta, status, data) 
        VALUES (?, ?, ?, FROM_UNIXTIME(?), 'queued', ?)
    ]], {
        Framework.GetPlayerIdentifier(source),
        recipeId,
        quantity,
        eta,
        json.encode({
            playerLevel = playerData.level,
            smeltTime = smeltTime
        })
    }, function(jobId)
        if jobId then
            local job = {
                id = jobId,
                owner = Framework.GetPlayerIdentifier(source),
                recipeId = recipeId,
                quantity = quantity,
                startedAt = os.time(),
                eta = eta,
                status = 'queued',
                data = {
                    playerLevel = playerData.level,
                    smeltTime = smeltTime
                }
            }
            
            SmeltJobs[jobId] = job
            
            Framework.Notify(source, Utils.Locale('smelting_queue'), 'success')
            TriggerClientEvent('ZGMining:SmeltStarted', source, job)
            
            -- Trigger smelting event
            TriggerEvent('ZGMining:SmeltQueued', source, recipeId, quantity, jobId)
            
            Utils.Log('info', 'Player %s started smelting %s x%d (Job ID: %d)', 
                Framework.GetPlayerIdentifier(source), recipeId, quantity, jobId)
        else
            Framework.Notify(source, Utils.Locale('error_generic'), 'error')
        end
    end)
end)

-- Cancel smelting job
RegisterNetEvent('ZGMining:CancelSmelting', function(jobId)
    local source = source
    local job = SmeltJobs[jobId]
    
    if not job then
        Framework.Notify(source, Utils.Locale('error_generic'), 'error')
        return
    end
    
    if job.owner ~= Framework.GetPlayerIdentifier(source) then
        Framework.Notify(source, Utils.Locale('permission_denied'), 'error')
        return
    end
    
    if job.status == 'completed' then
        Framework.Notify(source, 'Job already completed', 'warning')
        return
    end
    
    -- Cancel the job
    job.status = 'cancelled'
    MySQL.update('UPDATE mining_smelt_jobs SET status = ? WHERE id = ?', {'cancelled', jobId})
    
    -- Refund materials (with penalty if already started)
    local recipe = Recipes.GetRecipe(job.recipeId)
    if recipe then
        local refundRate = job.status == 'queued' and 1.0 or 0.5 -- 50% refund if already started
        
        for _, input in pairs(recipe.inputs) do
            local refundAmount = math.floor(input.count * job.quantity * refundRate)
            if refundAmount > 0 then
                Framework.AddItem(source, input.item, refundAmount)
            end
        end
    end
    
    -- Remove from active smelts
    if ActiveSmelts[jobId] then
        ActiveSmelts[jobId] = nil
    end
    
    Framework.Notify(source, Utils.Locale('smelt_cancelled'), 'info')
    TriggerClientEvent('ZGMining:SmeltCancelled', source, jobId)
end)

-- Get player's smelt jobs
RegisterNetEvent('ZGMining:GetSmeltJobs', function()
    local source = source
    local identifier = Framework.GetPlayerIdentifier(source)
    local playerJobs = {}
    
    for _, job in pairs(SmeltJobs) do
        if job.owner == identifier and job.status ~= 'cancelled' then
            local recipe = Recipes.GetRecipe(job.recipeId)
            local jobData = {
                id = job.id,
                recipe = recipe,
                quantity = job.quantity,
                status = job.status,
                eta = job.eta,
                progress = 0
            }
            
            -- Calculate progress
            if job.status == 'active' then
                local elapsed = os.time() - job.startedAt
                local totalTime = job.eta - job.startedAt
                jobData.progress = math.min(100, (elapsed / totalTime) * 100)
            elseif job.status == 'completed' then
                jobData.progress = 100
            end
            
            table.insert(playerJobs, jobData)
        end
    end
    
    TriggerClientEvent('ZGMining:ReceiveSmeltJobs', source, playerJobs)
end)

-- Collect completed smelt
RegisterNetEvent('ZGMining:CollectSmelt', function(jobId)
    local source = source
    local job = SmeltJobs[jobId]
    
    if not job then
        Framework.Notify(source, Utils.Locale('error_generic'), 'error')
        return
    end
    
    if job.owner ~= Framework.GetPlayerIdentifier(source) then
        Framework.Notify(source, Utils.Locale('permission_denied'), 'error')
        return
    end
    
    if job.status ~= 'completed' then
        Framework.Notify(source, 'Smelting not completed yet', 'warning')
        return
    end
    
    local recipe = Recipes.GetRecipe(job.recipeId)
    if not recipe then
        Framework.Notify(source, Utils.Locale('error_invalid_recipe'), 'error')
        return
    end
    
    -- Give output items
    for _, output in pairs(recipe.outputs) do
        local outputAmount = output.count * job.quantity
        if Framework.AddItem(source, output.item, outputAmount) then
            local itemLabel = Items.All[output.item] and Items.All[output.item].label or output.item
            Framework.Notify(source, Utils.Locale('item_received', itemLabel, outputAmount), 'success')
        end
    end
    
    -- Give XP
    local playerData = exports['ZGMining']:GetPlayerMiningData(source)
    if playerData then
        local xpGain = recipe.xp * job.quantity
        exports['ZGMining']:AddMiningXP(source, xpGain)
        
        -- Update smelting stats
        playerData.stats.items_smelted = playerData.stats.items_smelted + job.quantity
    end
    
    -- Remove job from database
    MySQL.execute('DELETE FROM mining_smelt_jobs WHERE id = ?', {jobId})
    SmeltJobs[jobId] = nil
    
    Framework.Notify(source, Utils.Locale('smelting_complete'), 'success')
    TriggerClientEvent('ZGMining:SmeltCollected', source, jobId)
    
    -- Trigger completion event
    TriggerEvent('ZGMining:SmeltComplete', source, job.recipeId, job.quantity, jobId)
end)

-- Smelting processing thread
CreateThread(function()
    while true do
        Wait(10000) -- Check every 10 seconds
        
        local currentTime = os.time()
        
        -- Process queued jobs
        for jobId, job in pairs(SmeltJobs) do
            if job.status == 'queued' then
                -- Start the job
                job.status = 'active'
                ActiveSmelts[jobId] = job
                MySQL.update('UPDATE mining_smelt_jobs SET status = ? WHERE id = ?', {'active', jobId})
                
                -- Notify player
                local playerId = Framework.GetPlayerByIdentifier(job.owner)
                if playerId then
                    TriggerClientEvent('ZGMining:SmeltStarted', playerId, job)
                end
            elseif job.status == 'active' and currentTime >= job.eta then
                -- Complete the job
                job.status = 'completed'
                ActiveSmelts[jobId] = nil
                MySQL.update('UPDATE mining_smelt_jobs SET status = ? WHERE id = ?', {'completed', jobId})
                
                -- Notify player
                local playerId = Framework.GetPlayerByIdentifier(job.owner)
                if playerId then
                    Framework.Notify(playerId, Utils.Locale('smelting_complete'), 'success')
                    TriggerClientEvent('ZGMining:SmeltCompleted', playerId, jobId)
                end
            end
        end
    end
end)

-- Helper function to get player by identifier
function Framework.GetPlayerByIdentifier(identifier)
    local players = GetPlayers()
    for _, playerId in pairs(players) do
        if Framework.GetPlayerIdentifier(tonumber(playerId)) == identifier then
            return tonumber(playerId)
        end
    end
    return nil
end

-- Initialize on resource start
CreateThread(function()
    Wait(2000) -- Wait for framework and database to initialize
    InitializeSmelting()
end)

-- Exports
exports('AddSmeltRecipe', function(recipe)
    if recipe.id and not Recipes.Smelting[recipe.id] then
        Recipes.Smelting[recipe.id] = recipe
        Utils.Log('info', 'Added custom smelt recipe: %s', recipe.id)
        return true
    end
    return false
end)

exports('GetSmeltJobs', function(identifier)
    local jobs = {}
    for _, job in pairs(SmeltJobs) do
        if not identifier or job.owner == identifier then
            table.insert(jobs, job)
        end
    end
    return jobs
end)
