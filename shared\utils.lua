Utils = {}

-- Localization
function Utils.Locale(key, ...)
    local locale = Locales[Config.Locale] or Locales['en']
    local text = locale[key] or key
    
    if ... then
        return string.format(text, ...)
    end
    
    return text
end

-- Math utilities
function Utils.Round(num, decimals)
    local mult = 10^(decimals or 0)
    return math.floor(num * mult + 0.5) / mult
end

function Utils.Clamp(value, min, max)
    return math.max(min, math.min(max, value))
end

function Utils.Lerp(a, b, t)
    return a + (b - a) * t
end

-- Table utilities
function Utils.TableLength(t)
    local count = 0
    for _ in pairs(t) do count = count + 1 end
    return count
end

function Utils.TableCopy(t)
    local copy = {}
    for k, v in pairs(t) do
        if type(v) == 'table' then
            copy[k] = Utils.TableCopy(v)
        else
            copy[k] = v
        end
    end
    return copy
end

function Utils.TableMerge(t1, t2)
    local result = Utils.TableCopy(t1)
    for k, v in pairs(t2) do
        if type(v) == 'table' and type(result[k]) == 'table' then
            result[k] = Utils.TableMerge(result[k], v)
        else
            result[k] = v
        end
    end
    return result
end

-- String utilities
function Utils.FormatNumber(num)
    if num >= 1000000 then
        return string.format("%.1fM", num / 1000000)
    elseif num >= 1000 then
        return string.format("%.1fK", num / 1000)
    else
        return tostring(num)
    end
end

function Utils.FormatTime(seconds)
    if seconds < 60 then
        return Utils.Locale('seconds', seconds)
    elseif seconds < 3600 then
        return Utils.Locale('minutes', math.floor(seconds / 60))
    elseif seconds < 86400 then
        local hours = math.floor(seconds / 3600)
        local minutes = math.floor((seconds % 3600) / 60)
        return Utils.Locale('time_format', hours, minutes, 0)
    else
        return Utils.Locale('days', math.floor(seconds / 86400))
    end
end

function Utils.FormatMoney(amount)
    return '$' .. Utils.FormatNumber(amount)
end

-- Vector utilities
function Utils.GetDistance(pos1, pos2)
    if type(pos1) == 'vector3' and type(pos2) == 'vector3' then
        return #(pos1 - pos2)
    elseif type(pos1) == 'table' and type(pos2) == 'table' then
        local dx = pos1.x - pos2.x
        local dy = pos1.y - pos2.y
        local dz = pos1.z - pos2.z
        return math.sqrt(dx*dx + dy*dy + dz*dz)
    end
    return 0
end

function Utils.GetDistance2D(pos1, pos2)
    if type(pos1) == 'vector3' and type(pos2) == 'vector3' then
        local dx = pos1.x - pos2.x
        local dy = pos1.y - pos2.y
        return math.sqrt(dx*dx + dy*dy)
    elseif type(pos1) == 'table' and type(pos2) == 'table' then
        local dx = pos1.x - pos2.x
        local dy = pos1.y - pos2.y
        return math.sqrt(dx*dx + dy*dy)
    end
    return 0
end

-- XP and Level calculations
function Utils.CalculateXPForLevel(level)
    if level <= 1 then return 0 end
    local totalXP = 0
    for i = 2, level do
        totalXP = totalXP + math.floor(Config.XP.base * (Config.XP.scale ^ (i - 2)))
    end
    return totalXP
end

function Utils.CalculateLevelFromXP(xp)
    local level = 1
    local totalXP = 0
    
    while level < Config.XP.maxLevel do
        local nextLevelXP = math.floor(Config.XP.base * (Config.XP.scale ^ (level - 1)))
        if totalXP + nextLevelXP > xp then
            break
        end
        totalXP = totalXP + nextLevelXP
        level = level + 1
    end
    
    return level, xp - totalXP
end

function Utils.GetXPForNextLevel(currentLevel)
    if currentLevel >= Config.XP.maxLevel then return 0 end
    return math.floor(Config.XP.base * (Config.XP.scale ^ (currentLevel - 1)))
end

-- Durability calculations
function Utils.CalculateDurabilityLoss(pickaxeTier, isHit, isCritFail)
    local baseLoss = Config.Pickaxes.durabilityLoss[pickaxeTier] or 1.0
    
    if not isHit then
        return baseLoss * 0.1 -- Minimal loss for missed swings
    end
    
    if isCritFail then
        local critDamage = math.random(Config.Pickaxes.critFailDamage.min, Config.Pickaxes.critFailDamage.max)
        return baseLoss + critDamage
    end
    
    -- Add some randomness
    return baseLoss * math.random(80, 120) / 100
end

function Utils.ShouldCritFail(pickaxeTier)
    local chance = Config.Pickaxes.critFailChance[pickaxeTier] or 0.01
    return math.random() < chance
end

-- Economy calculations
function Utils.CalculateSellPrice(item, basePrice, level, jobMultiplier)
    local price = basePrice
    
    -- Level bonus
    if level and Config.XP.perks.sellBonus then
        local levelBonus = math.floor(level / 10) * Config.XP.perks.sellBonus
        levelBonus = math.min(levelBonus, 0.25) -- Max 25% bonus
        price = price * (1 + levelBonus)
    end
    
    -- Job multiplier
    if jobMultiplier then
        price = price * jobMultiplier
    end
    
    -- Apply tax
    price = price * (1 - Config.Economy.tax)
    
    return Utils.Round(price, 2)
end

-- Validation utilities
function Utils.IsValidCoords(coords)
    return coords and coords.x and coords.y and coords.z
end

function Utils.IsPlayerNearCoords(playerId, coords, distance)
    local playerCoords = GetEntityCoords(GetPlayerPed(playerId))
    return Utils.GetDistance(playerCoords, coords) <= distance
end

-- Debug utilities
function Utils.Debug(message, ...)
    if Config.Debug then
        print(string.format('[ZG Mining] %s', string.format(message, ...)))
    end
end

function Utils.Log(level, message, ...)
    local formattedMessage = string.format('[ZG Mining] [%s] %s', level:upper(), string.format(message, ...))
    print(formattedMessage)
end

-- Random utilities
function Utils.GetRandomOreType(level, biome)
    local availableOres = {}
    
    for oreType, data in pairs(Config.Rocks.health) do
        -- Check level requirements (you can add this to config)
        local levelReq = 1
        if oreType == 'iron' then levelReq = 5
        elseif oreType == 'silver' then levelReq = 10
        elseif oreType == 'gold' then levelReq = 20
        elseif oreType == 'titanium' then levelReq = 30
        elseif oreType == 'diamond' then levelReq = 40
        end
        
        if level >= levelReq then
            table.insert(availableOres, oreType)
        end
    end
    
    if #availableOres == 0 then
        return 'copper' -- Fallback
    end
    
    return availableOres[math.random(#availableOres)]
end

-- Grid utilities
function Utils.GenerateGridPositions(center, rows, cols, spacing, yaw, jitter)
    local positions = {}
    local startX = -(cols - 1) * spacing / 2
    local startY = -(rows - 1) * spacing / 2
    
    local cosYaw = math.cos(math.rad(yaw))
    local sinYaw = math.sin(math.rad(yaw))
    
    for row = 1, rows do
        for col = 1, cols do
            local localX = startX + (col - 1) * spacing
            local localY = startY + (row - 1) * spacing
            
            -- Apply jitter
            if jitter > 0 then
                localX = localX + (math.random() - 0.5) * jitter * spacing
                localY = localY + (math.random() - 0.5) * jitter * spacing
            end
            
            -- Rotate around center
            local worldX = center.x + (localX * cosYaw - localY * sinYaw)
            local worldY = center.y + (localX * sinYaw + localY * cosYaw)
            
            -- Get ground Z
            local groundZ = center.z
            local hit, coords = GetGroundZFor_3dCoord(worldX, worldY, center.z + 50.0, false)
            if hit then
                groundZ = coords.z
            end
            
            table.insert(positions, vector3(worldX, worldY, groundZ))
        end
    end
    
    return positions
end

return Utils
