# Mining System Sounds

This directory contains all the sound effects used in the mining system.

## Required Sound Files (OGG format)

### Mining Sounds
- `mine_hit_01.ogg` - Pickaxe hitting rock (success)
- `mine_hit_02.ogg` - Pickaxe hitting rock (alternate)
- `mine_hit_03.ogg` - Pickaxe hitting rock (alternate)
- `mine_miss.ogg` - Pickaxe missing/glancing blow
- `rock_break.ogg` - Rock being depleted/destroyed
- `pickaxe_break.ogg` - Pickaxe breaking sound

### Smelting Sounds
- `furnace_start.ogg` - Furnace starting up
- `smelting_loop.ogg` - Smelting process (loopable)
- `smelt_complete.ogg` - Smelting completion bell/chime
- `furnace_ambient.ogg` - Background furnace ambience

### UI Sounds
- `ui_click.ogg` - Button click sound
- `ui_hover.ogg` - Button hover sound
- `ui_open.ogg` - Menu opening sound
- `ui_close.ogg` - Menu closing sound
- `ui_error.ogg` - Error notification sound
- `ui_success.ogg` - Success notification sound
- `ui_warning.ogg` - Warning notification sound

### Level/XP Sounds
- `xp_gain.ogg` - XP gained sound
- `level_up.ogg` - Level up fanfare
- `achievement.ogg` - Achievement unlocked sound

### Economy Sounds
- `coins_drop.ogg` - Money received sound
- `cash_register.ogg` - Sale completion sound
- `item_pickup.ogg` - Item received sound

## Sound Guidelines

- **Format**: OGG Vorbis (better compression than MP3)
- **Quality**: 44.1kHz, 16-bit, stereo
- **Length**: Keep sounds short (0.5-3 seconds for effects)
- **Volume**: Normalize to -6dB to prevent clipping
- **Looping**: Mark loopable sounds clearly in filename

## Volume Levels

Recommended volume levels for different sound types:
- **UI Sounds**: -12dB to -18dB (subtle)
- **Mining Sounds**: -6dB to -12dB (prominent but not overwhelming)
- **Ambient Sounds**: -18dB to -24dB (background)
- **Notification Sounds**: -9dB to -15dB (attention-getting)

## Implementation

Sounds are played through the NUI system using HTML5 audio:

```javascript
// Play a sound effect
function playSound(soundName) {
    const audio = new Audio(`sounds/${soundName}.ogg`);
    audio.volume = 0.5; // Adjust as needed
    audio.play();
}
```

## Creating Custom Sounds

When creating or sourcing sounds:
1. **Keep it thematic**: Sounds should fit the mining/industrial theme
2. **Avoid copyright issues**: Use royalty-free or original sounds
3. **Test in-game**: Ensure sounds work well with game audio
4. **Consider player preferences**: Provide volume controls
5. **Optimize file sizes**: Balance quality with download size

## Sound Sources

Recommended sources for royalty-free sounds:
- Freesound.org
- Zapsplat.com
- Adobe Audition built-in library
- Custom recordings with proper equipment

## Installation

1. Place all OGG files in this directory
2. Ensure filenames match the code references
3. Test all sounds in-game
4. Adjust volumes as needed for your server

## Troubleshooting

Common sound issues:
- **No sound**: Check file format and browser compatibility
- **Too loud/quiet**: Adjust volume levels in code
- **Crackling**: Check for audio clipping, reduce volume
- **Won't play**: Verify file paths and permissions
