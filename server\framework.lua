Framework = {}

-- Framework Detection
local detectedFramework = nil

function Framework.Detect()
    if Config.Framework ~= 'auto' then
        detectedFramework = Config.Framework
        Utils.Debug('Framework manually set to: %s', detectedFramework)
        return detectedFramework
    end
    
    if GetResourceState('qbox-core') == 'started' then
        detectedFramework = 'qbox'
    elseif GetResourceState('qb-core') == 'started' then
        detectedFramework = 'qbcore'
    elseif GetResourceState('es_extended') == 'started' then
        detectedFramework = 'esx'
    else
        detectedFramework = 'standalone'
    end
    
    Utils.Debug('Framework auto-detected: %s', detectedFramework)
    return detectedFramework
end

-- Initialize Framework
function Framework.Init()
    local fw = Framework.Detect()
    
    if fw == 'qbox' then
        Framework.Core = exports['qbox-core']:GetCoreObject()
    elseif fw == 'qbcore' then
        Framework.Core = exports['qb-core']:GetCoreObject()
    elseif fw == 'esx' then
        Framework.Core = exports['es_extended']:getSharedObject()
    end
    
    Utils.Log('info', 'Framework initialized: %s', fw)
end

-- Unified Framework API
function Framework.GetPlayer(source)
    local fw = detectedFramework or Framework.Detect()
    
    if fw == 'qbox' or fw == 'qbcore' then
        return Framework.Core.Functions.GetPlayer(source)
    elseif fw == 'esx' then
        return Framework.Core.GetPlayerFromId(source)
    end
    
    return nil
end

function Framework.GetPlayerIdentifier(source)
    local player = Framework.GetPlayer(source)
    if not player then return nil end
    
    local fw = detectedFramework or Framework.Detect()
    
    if fw == 'qbox' or fw == 'qbcore' then
        return player.PlayerData.citizenid
    elseif fw == 'esx' then
        return player.identifier
    end
    
    return tostring(source)
end

function Framework.AddMoney(source, account, amount, reason)
    local player = Framework.GetPlayer(source)
    if not player then return false end
    
    local fw = detectedFramework or Framework.Detect()
    
    if fw == 'qbox' or fw == 'qbcore' then
        return player.Functions.AddMoney(account or 'cash', amount, reason)
    elseif fw == 'esx' then
        if account == 'cash' then account = 'money' end
        player.addMoney(amount)
        return true
    end
    
    return false
end

function Framework.RemoveMoney(source, account, amount, reason)
    local player = Framework.GetPlayer(source)
    if not player then return false end
    
    local fw = detectedFramework or Framework.Detect()
    
    if fw == 'qbox' or fw == 'qbcore' then
        return player.Functions.RemoveMoney(account or 'cash', amount, reason)
    elseif fw == 'esx' then
        if account == 'cash' then account = 'money' end
        if player.getMoney() >= amount then
            player.removeMoney(amount)
            return true
        end
        return false
    end
    
    return false
end

function Framework.GetMoney(source, account)
    local player = Framework.GetPlayer(source)
    if not player then return 0 end
    
    local fw = detectedFramework or Framework.Detect()
    
    if fw == 'qbox' or fw == 'qbcore' then
        return player.PlayerData.money[account or 'cash'] or 0
    elseif fw == 'esx' then
        if account == 'cash' then account = 'money' end
        return player.getMoney() or 0
    end
    
    return 0
end

function Framework.AddItem(source, name, count, metadata)
    local player = Framework.GetPlayer(source)
    if not player then return false end
    
    local fw = detectedFramework or Framework.Detect()
    
    if fw == 'qbox' or fw == 'qbcore' then
        return player.Functions.AddItem(name, count, false, metadata)
    elseif fw == 'esx' then
        player.addInventoryItem(name, count)
        return true
    end
    
    return false
end

function Framework.RemoveItem(source, name, count, metadata)
    local player = Framework.GetPlayer(source)
    if not player then return false end
    
    local fw = detectedFramework or Framework.Detect()
    
    if fw == 'qbox' or fw == 'qbcore' then
        return player.Functions.RemoveItem(name, count, false, metadata)
    elseif fw == 'esx' then
        player.removeInventoryItem(name, count)
        return true
    end
    
    return false
end

function Framework.HasItem(source, name, count)
    local player = Framework.GetPlayer(source)
    if not player then return false end
    
    local fw = detectedFramework or Framework.Detect()
    count = count or 1
    
    if fw == 'qbox' or fw == 'qbcore' then
        local item = player.Functions.GetItemByName(name)
        return item and item.amount >= count
    elseif fw == 'esx' then
        local item = player.getInventoryItem(name)
        return item and item.count >= count
    end
    
    return false
end

function Framework.GetItemCount(source, name)
    local player = Framework.GetPlayer(source)
    if not player then return 0 end
    
    local fw = detectedFramework or Framework.Detect()
    
    if fw == 'qbox' or fw == 'qbcore' then
        local item = player.Functions.GetItemByName(name)
        return item and item.amount or 0
    elseif fw == 'esx' then
        local item = player.getInventoryItem(name)
        return item and item.count or 0
    end
    
    return 0
end

function Framework.Notify(source, text, type, time)
    Bridges.Notify.Send(source, text, type, time)
end

function Framework.RegisterItem(item, data)
    local fw = detectedFramework or Framework.Detect()
    
    if fw == 'qbox' or fw == 'qbcore' then
        -- Items are typically registered in qb-core/shared/items.lua
        -- This is more for validation
        Utils.Debug('Item %s should be added to qb-core items.lua', item)
    elseif fw == 'esx' then
        -- Items are typically registered in es_extended/config.lua
        Utils.Debug('Item %s should be added to ESX items config', item)
    end
end

function Framework.HasPermission(source, permission)
    if IsPlayerAceAllowed(source, permission) then
        return true
    end
    
    local player = Framework.GetPlayer(source)
    if not player then return false end
    
    local fw = detectedFramework or Framework.Detect()
    
    if fw == 'qbox' or fw == 'qbcore' then
        -- Check for admin permission
        return Framework.Core.Functions.HasPermission(source, 'admin') or 
               player.PlayerData.job.name == 'admin'
    elseif fw == 'esx' then
        -- Check for admin group
        return player.getGroup() == 'admin' or player.getGroup() == 'superadmin'
    end
    
    return false
end

function Framework.GetJob(source)
    local player = Framework.GetPlayer(source)
    if not player then return nil end
    
    local fw = detectedFramework or Framework.Detect()
    
    if fw == 'qbox' or fw == 'qbcore' then
        return player.PlayerData.job.name
    elseif fw == 'esx' then
        return player.job.name
    end
    
    return nil
end

function Framework.IsJobAllowed(source, allowedJobs)
    if not allowedJobs or #allowedJobs == 0 then return true end
    
    local job = Framework.GetJob(source)
    if not job then return false end
    
    for _, allowedJob in pairs(allowedJobs) do
        if job == allowedJob then
            return true
        end
    end
    
    return false
end

-- Initialize on resource start
CreateThread(function()
    Framework.Init()
end)
