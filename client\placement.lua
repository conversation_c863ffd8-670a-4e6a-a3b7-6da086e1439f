-- ZG Mining System - 3D Placement System

local PlacementMode = false
local PlacementData = {
    center = vector3(0, 0, 0),
    rows = 5,
    cols = 5,
    spacing = 3.0,
    yaw = 0.0,
    jitter = 0.1,
    scale = 1.0,
    oreComposition = {
        copper = 0.4,
        iron = 0.3,
        silver = 0.2,
        gold = 0.1
    }
}

local GhostProps = {}
local PreviewPositions = {}

-- Open placement UI
function OpenPlacementUI()
    if not Framework.HasPermission(PlayerId(), Config.Placement.permissions.place) then
        Framework.Notify(Utils.Locale('permission_denied'), 'error')
        return
    end
    
    PlacementMode = true
    SetNuiFocus(true, true)
    
    SendNUIMessage({
        action = 'openPlacement',
        data = PlacementData,
        config = Config.Placement.defaults
    })
    
    -- Start placement preview
    StartPlacementPreview()
end

-- Close placement UI
function ClosePlacementUI()
    PlacementMode = false
    SetNuiFocus(false, false)
    
    SendNUIMessage({
        action = 'closePlacement'
    })
    
    -- Clear preview
    ClearPlacementPreview()
end

-- Start placement preview
function StartPlacementPreview()
    CreateThread(function()
        while PlacementMode do
            Wait(0)
            
            -- Get camera raycast
            local hit, coords, normal, entity = GetCameraRaycast()
            
            if hit then
                PlacementData.center = coords
                UpdatePlacementPreview()
            end
            
            -- Handle input
            HandlePlacementInput()
        end
    end)
end

-- Update placement preview
function UpdatePlacementPreview()
    -- Clear existing props
    ClearGhostProps()
    
    -- Generate new positions
    PreviewPositions = Utils.GenerateGridPositions(
        PlacementData.center,
        PlacementData.rows,
        PlacementData.cols,
        PlacementData.spacing,
        PlacementData.yaw,
        PlacementData.jitter
    )
    
    -- Limit preview props for performance
    local maxProps = math.min(#PreviewPositions, Config.Performance.maxGhostProps)
    
    for i = 1, maxProps do
        local pos = PreviewPositions[i]
        CreateGhostProp(pos, i)
    end
    
    -- Draw grid lines
    DrawGridLines()
end

-- Create ghost prop
function CreateGhostProp(coords, index)
    local model = Config.Placement.ghostProp
    
    RequestModel(model)
    while not HasModelLoaded(model) do
        Wait(100)
    end
    
    local prop = CreateObject(model, coords.x, coords.y, coords.z, false, false, false)
    SetEntityAlpha(prop, 150, false)
    SetEntityCollision(prop, false, false)
    FreezeEntityPosition(prop, true)
    
    -- Color based on validity
    local isValid = ValidatePlacementPosition(coords)
    if isValid then
        SetEntityRenderScorched(prop, false)
    else
        SetEntityRenderScorched(prop, true) -- Red tint for invalid positions
    end
    
    GhostProps[index] = prop
end

-- Clear ghost props
function ClearGhostProps()
    for _, prop in pairs(GhostProps) do
        if DoesEntityExist(prop) then
            DeleteObject(prop)
        end
    end
    GhostProps = {}
end

-- Clear placement preview
function ClearPlacementPreview()
    ClearGhostProps()
    PreviewPositions = {}
end

-- Draw grid lines
function DrawGridLines()
    if #PreviewPositions < 2 then return end
    
    local center = PlacementData.center
    local rows = PlacementData.rows
    local cols = PlacementData.cols
    local spacing = PlacementData.spacing
    local yaw = math.rad(PlacementData.yaw)
    
    local cosYaw = math.cos(yaw)
    local sinYaw = math.sin(yaw)
    
    -- Draw row lines
    for row = 1, rows do
        local startCol = 1
        local endCol = cols
        
        local startIndex = (row - 1) * cols + startCol
        local endIndex = (row - 1) * cols + endCol
        
        if PreviewPositions[startIndex] and PreviewPositions[endIndex] then
            local startPos = PreviewPositions[startIndex]
            local endPos = PreviewPositions[endIndex]
            DrawLine(startPos.x, startPos.y, startPos.z + 0.1, 
                    endPos.x, endPos.y, endPos.z + 0.1, 0, 255, 255, 100)
        end
    end
    
    -- Draw column lines
    for col = 1, cols do
        local startRow = 1
        local endRow = rows
        
        local startIndex = (startRow - 1) * cols + col
        local endIndex = (endRow - 1) * cols + col
        
        if PreviewPositions[startIndex] and PreviewPositions[endIndex] then
            local startPos = PreviewPositions[startIndex]
            local endPos = PreviewPositions[endIndex]
            DrawLine(startPos.x, startPos.y, startPos.z + 0.1, 
                    endPos.x, endPos.y, endPos.z + 0.1, 0, 255, 255, 100)
        end
    end
    
    -- Draw center marker
    DrawMarker(1, center.x, center.y, center.z, 0.0, 0.0, 0.0, 0.0, 0.0, PlacementData.yaw, 
              2.0, 2.0, 1.0, 255, 255, 0, 150, false, true, 2, false, nil, nil, false)
end

-- Validate placement position
function ValidatePlacementPosition(coords)
    -- Check for collisions
    local hit = StartShapeTestRay(coords.x, coords.y, coords.z + 2.0, 
                                 coords.x, coords.y, coords.z - 2.0, 
                                 -1, 0, 0)
    local _, hit2, _, _, entity = GetShapeTestResult(hit)
    
    if hit2 and entity ~= 0 then
        return false -- Collision detected
    end
    
    -- Check navmesh
    local navmeshValid = IsPointOnRoad(coords.x, coords.y, coords.z, 0)
    if navmeshValid then
        return false -- On road
    end
    
    -- Check water
    local waterHeight = GetWaterHeight(coords.x, coords.y, coords.z)
    if waterHeight > coords.z - 1.0 then
        return false -- In water
    end
    
    return true
end

-- Handle placement input
function HandlePlacementInput()
    -- Rotate grid
    if IsControlPressed(0, 21) then -- Left Shift
        if IsControlPressed(0, 34) then -- A
            PlacementData.yaw = PlacementData.yaw - 1.0
        elseif IsControlPressed(0, 35) then -- D
            PlacementData.yaw = PlacementData.yaw + 1.0
        end
        
        -- Snap to angles
        if Config.Placement.defaults.yawSnap > 0 then
            PlacementData.yaw = math.floor(PlacementData.yaw / Config.Placement.defaults.yawSnap) * Config.Placement.defaults.yawSnap
        end
        
        PlacementData.yaw = PlacementData.yaw % 360
    end
    
    -- Adjust spacing
    if IsControlPressed(0, 19) then -- Alt
        if IsControlJustPressed(0, 96) then -- Scroll Up
            PlacementData.spacing = math.min(PlacementData.spacing + 0.5, 10.0)
        elseif IsControlJustPressed(0, 97) then -- Scroll Down
            PlacementData.spacing = math.max(PlacementData.spacing - 0.5, 1.0)
        end
    end
    
    -- Adjust grid size
    if IsControlPressed(0, 36) then -- Left Ctrl
        if IsControlJustPressed(0, 96) then -- Scroll Up
            PlacementData.rows = math.min(PlacementData.rows + 1, 20)
            PlacementData.cols = math.min(PlacementData.cols + 1, 20)
        elseif IsControlJustPressed(0, 97) then -- Scroll Down
            PlacementData.rows = math.max(PlacementData.rows - 1, 1)
            PlacementData.cols = math.max(PlacementData.cols - 1, 1)
        end
    end
end

-- Get camera raycast
function GetCameraRaycast()
    local cam = GetRenderingCam()
    local camCoords = GetCamCoord(cam)
    local camRot = GetCamRot(cam, 2)
    
    local direction = RotationToDirection(camRot)
    local destination = camCoords + (direction * 1000.0)
    
    local hit = StartShapeTestRay(camCoords.x, camCoords.y, camCoords.z, 
                                 destination.x, destination.y, destination.z, 
                                 -1, 0, 0)
    
    local _, hit2, coords, normal, entity = GetShapeTestResult(hit)
    
    return hit2, coords, normal, entity
end

-- Convert rotation to direction
function RotationToDirection(rotation)
    local adjustedRotation = vector3(
        (math.pi / 180) * rotation.x,
        (math.pi / 180) * rotation.y,
        (math.pi / 180) * rotation.z
    )
    
    local direction = vector3(
        -math.sin(adjustedRotation.z) * math.abs(math.cos(adjustedRotation.x)),
        math.cos(adjustedRotation.z) * math.abs(math.cos(adjustedRotation.x)),
        math.sin(adjustedRotation.x)
    )
    
    return direction
end

-- NUI Callbacks for placement
RegisterNUICallback('updatePlacement', function(data, cb)
    if data.rows then PlacementData.rows = data.rows end
    if data.cols then PlacementData.cols = data.cols end
    if data.spacing then PlacementData.spacing = data.spacing end
    if data.yaw then PlacementData.yaw = data.yaw end
    if data.jitter then PlacementData.jitter = data.jitter end
    if data.scale then PlacementData.scale = data.scale end
    if data.oreComposition then PlacementData.oreComposition = data.oreComposition end
    
    cb('ok')
end)

RegisterNUICallback('bakeGrid', function(data, cb)
    if #PreviewPositions == 0 then
        Framework.Notify(Utils.Locale('invalid_location'), 'error')
        cb('error')
        return
    end
    
    -- Validate all positions
    local validPositions = {}
    for i, pos in pairs(PreviewPositions) do
        if ValidatePlacementPosition(pos) then
            table.insert(validPositions, {
                coords = pos,
                oreType = GetRandomOreTypeForPosition(i),
                index = i
            })
        end
    end
    
    if #validPositions == 0 then
        Framework.Notify(Utils.Locale('invalid_location'), 'error')
        cb('error')
        return
    end
    
    -- Send to server
    local gridData = {
        name = data.name or ('Grid_' .. os.time()),
        center = PlacementData.center,
        positions = validPositions,
        settings = PlacementData
    }
    
    TriggerServerEvent('ZGMining:BakeGrid', gridData)
    
    ClosePlacementUI()
    cb('ok')
end)

RegisterNUICallback('clearPreview', function(data, cb)
    ClearPlacementPreview()
    cb('ok')
end)

RegisterNUICallback('closePlacement', function(data, cb)
    ClosePlacementUI()
    cb('ok')
end)

-- Get random ore type based on composition
function GetRandomOreTypeForPosition(index)
    local rand = math.random()
    local cumulative = 0
    
    for oreType, weight in pairs(PlacementData.oreComposition) do
        cumulative = cumulative + weight
        if rand <= cumulative then
            return oreType
        end
    end
    
    return 'copper' -- Fallback
end

-- Event handlers
RegisterNetEvent('ZGMining:OpenPlacement', function()
    OpenPlacementUI()
end)

RegisterNetEvent('ZGMining:GridBaked', function(success, message)
    if success then
        Framework.Notify(Utils.Locale('grid_placed'), 'success')
    else
        Framework.Notify(message or Utils.Locale('error_generic'), 'error')
    end
end)

-- Exports
exports('OpenPlacementUI', OpenPlacementUI)
