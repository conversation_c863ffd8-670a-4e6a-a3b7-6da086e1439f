-- ZG Mining System - Client Main

local PlayerData = {}
local MiningGrids = {}
local NearbyRocks = {}
local CurrentMining = nil
local MenuOpen = false

-- Initialize client
CreateThread(function()
    while not NetworkIsPlayerActive(PlayerId()) do
        Wait(100)
    end
    
    -- Request player data
    TriggerServerEvent('ZGMining:RequestPlayerData')
    
    -- Setup keybinds
    RegisterKeyMapping('miningmenu', Utils.Locale('open_mining_menu'), 'keyboard', 'F5')
    RegisterCommand('miningmenu', function()
        if not MenuOpen then
            OpenMiningMenu()
        end
    end, false)
    
    -- Setup PEDs
    SetupPeds()
    
    Utils.Debug('Client initialized')
end)

-- Setup PEDs
function SetupPeds()
    for pedType, pedData in pairs(Config.Peds) do
        local coords = pedData.coords
        
        -- Create blip
        if pedData.blip then
            local blip = AddBlipForCoord(coords.x, coords.y, coords.z)
            SetBlipSprite(blip, pedData.blip.sprite)
            SetBlipColour(blip, pedData.blip.color)
            SetBlipScale(blip, pedData.blip.scale)
            SetBlipAsShortRange(blip, true)
            BeginTextCommandSetBlipName('STRING')
            AddTextComponentString(pedData.blip.label)
            EndTextCommandSetBlipName(blip)
        end
        
        -- Create PED
        RequestModel(pedData.model)
        while not HasModelLoaded(pedData.model) do
            Wait(100)
        end
        
        local ped = CreatePed(4, pedData.model, coords.x, coords.y, coords.z - 1.0, coords.w, false, true)
        SetEntityAsMissionEntity(ped, true, true)
        SetPedFleeAttributes(ped, 0, 0)
        SetPedDiesWhenInjured(ped, false)
        SetPedKeepTask(ped, true)
        SetBlockingOfNonTemporaryEvents(ped, true)
        FreezeEntityPosition(ped, true)
        
        -- Store PED reference
        if not _G.MiningPeds then _G.MiningPeds = {} end
        _G.MiningPeds[pedType] = ped
    end
end

-- Open Mining Menu
function OpenMiningMenu()
    if MenuOpen then return end

    MenuOpen = true
    SetNuiFocus(true, true)
    SetNuiFocusKeepInput(false)

    -- Send data to NUI
    SendNUIMessage({
        action = 'openMenu',
        playerData = PlayerData,
        config = {
            locale = Config.Locale,
            maxLevel = Config.XP.maxLevel
        }
    })
end

-- Close Mining Menu
function CloseMiningMenu()
    if not MenuOpen then return end

    MenuOpen = false
    SetNuiFocus(false, false)
    SetNuiFocusKeepInput(false)

    SendNUIMessage({
        action = 'closeMenu'
    })
end

-- NUI Callbacks
RegisterNUICallback('closeMenu', function(data, cb)
    CloseMiningMenu()
    cb('ok')
end)

RegisterNUICallback('requestData', function(data, cb)
    local responseData = {}
    
    if data.type == 'playerStats' then
        responseData = PlayerData
    elseif data.type == 'sellPrices' then
        TriggerServerEvent('ZGMining:GetSellPrices')
    elseif data.type == 'smeltJobs' then
        TriggerServerEvent('ZGMining:GetSmeltJobs')
    elseif data.type == 'leaderboards' then
        TriggerServerEvent('ZGMining:GetLeaderboards')
    end
    
    cb(responseData)
end)

RegisterNUICallback('sellItems', function(data, cb)
    if data.items and #data.items > 0 then
        TriggerServerEvent('ZGMining:SellItems', data.items)
    end
    cb('ok')
end)

RegisterNUICallback('startSmelting', function(data, cb)
    if data.recipeId and data.quantity then
        TriggerServerEvent('ZGMining:StartSmelting', data.recipeId, data.quantity)
    end
    cb('ok')
end)

RegisterNUICallback('cancelSmelting', function(data, cb)
    if data.jobId then
        TriggerServerEvent('ZGMining:CancelSmelting', data.jobId)
    end
    cb('ok')
end)

RegisterNUICallback('collectSmelt', function(data, cb)
    if data.jobId then
        TriggerServerEvent('ZGMining:CollectSmelt', data.jobId)
    end
    cb('ok')
end)

RegisterNUICallback('purchaseItem', function(data, cb)
    if data.item and data.quantity then
        TriggerServerEvent('ZGMining:PurchaseItem', data.item, data.quantity)
    end
    cb('ok')
end)

-- Mining Functions
function StartMining(rockId)
    if CurrentMining then
        Framework.Notify(Utils.Locale('mining_in_progress'), 'warning')
        return
    end
    
    -- Check if player has a pickaxe
    local pickaxe = GetPlayerPickaxe()
    if not pickaxe then
        Framework.Notify('You need a pickaxe to mine!', 'error')
        return
    end
    
    CurrentMining = {
        rockId = rockId,
        pickaxe = pickaxe,
        startTime = GetGameTimer()
    }
    
    TriggerServerEvent('ZGMining:StartMining', rockId, pickaxe)
end

function StopMining()
    if not CurrentMining then return end
    
    CurrentMining = nil
    TriggerServerEvent('ZGMining:StopMining')
    
    -- Stop any active animations
    ClearPedTasks(PlayerPedId())
end

function GetPlayerPickaxe()
    -- This would integrate with the inventory system
    -- For now, return a basic pickaxe
    return {
        name = 'pickaxe_wood',
        durability = 50
    }
end

function PerformMiningHit(rockId)
    if not CurrentMining or CurrentMining.rockId ~= rockId then return end
    
    local playerPed = PlayerPedId()
    
    -- Play mining animation
    RequestAnimDict('melee@large_wpn@streamed_core')
    while not HasAnimDictLoaded('melee@large_wpn@streamed_core') do
        Wait(100)
    end
    
    TaskPlayAnim(playerPed, 'melee@large_wpn@streamed_core', 'ground_attack_on_spot', 8.0, -8.0, -1, 0, 0, false, false, false)
    
    -- Skill check
    Bridges.SkillCheck.Start({'easy', 'medium'}, function(success)
        TriggerServerEvent('ZGMining:MiningHit', rockId, success)
        
        -- Play hit sound
        PlaySoundFrontend(-1, success and 'PICK_UP' or 'ERROR', 'HUD_FRONTEND_DEFAULT_SOUNDSET', false)
        
        -- Camera shake on successful hit
        if success then
            ShakeGameplayCam('SMALL_EXPLOSION_SHAKE', 0.1)
        end
    end)
end

-- Rock Management
function UpdateNearbyRocks()
    local playerCoords = GetEntityCoords(PlayerPedId())
    NearbyRocks = {}
    
    for gridId, grid in pairs(MiningGrids) do
        if grid.rocks then
            for rockId, rock in pairs(grid.rocks) do
                if rock.coords then
                    local distance = Utils.GetDistance(playerCoords, rock.coords)
                    if distance <= 50.0 then -- 50m range
                        NearbyRocks[rockId] = {
                            id = rockId,
                            coords = rock.coords,
                            oreType = rock.oreType,
                            health = rock.health,
                            maxHealth = rock.maxHealth,
                            depleted = rock.depleted,
                            distance = distance
                        }
                    end
                end
            end
        end
    end
end

-- Rock interaction thread
CreateThread(function()
    while true do
        Wait(1000)
        UpdateNearbyRocks()
    end
end)

-- Draw rock markers
CreateThread(function()
    while true do
        Wait(0)
        
        local playerCoords = GetEntityCoords(PlayerPedId())
        
        for rockId, rock in pairs(NearbyRocks) do
            if rock.distance <= 10.0 then
                local coords = rock.coords
                
                -- Draw marker
                if not rock.depleted then
                    DrawMarker(1, coords.x, coords.y, coords.z - 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 
                              1.5, 1.5, 0.5, 0, 255, 0, 100, false, true, 2, false, nil, nil, false)
                    
                    -- Draw text
                    if rock.distance <= 3.0 then
                        local onScreen, screenX, screenY = World3dToScreen2d(coords.x, coords.y, coords.z + 1.0)
                        if onScreen then
                            SetTextScale(0.35, 0.35)
                            SetTextFont(4)
                            SetTextProportional(1)
                            SetTextColour(255, 255, 255, 215)
                            SetTextEntry('STRING')
                            SetTextCentre(true)
                            AddTextComponentString(string.format('%s Rock\nHealth: %d%%', 
                                rock.oreType:gsub('^%l', string.upper), 
                                math.floor((rock.health / rock.maxHealth) * 100)))
                            DrawText(screenX, screenY)
                        end
                        
                        -- Interaction prompt
                        if Config.Target.system == 'text' then
                            SetTextComponentFormat('STRING')
                            AddTextComponentString(string.format('~INPUT_CONTEXT~ %s', Utils.Locale('start_mining')))
                            DisplayHelpTextFromStringLabel(0, 0, 1, -1)
                            
                            if IsControlJustPressed(0, Config.Controls.interact) then
                                StartMining(rockId)
                            end
                        end
                    end
                else
                    -- Depleted rock marker
                    DrawMarker(1, coords.x, coords.y, coords.z - 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 
                              1.5, 1.5, 0.5, 255, 0, 0, 100, false, true, 2, false, nil, nil, false)
                end
            end
        end
    end
end)

-- Event Handlers
RegisterNetEvent('ZGMining:PlayerDataLoaded', function(data)
    PlayerData = data
    Utils.Debug('Player data loaded: Level %d, XP %d', data.level, data.xp)
end)

RegisterNetEvent('ZGMining:UpdatePlayerData', function(data)
    PlayerData = data
    
    -- Update NUI if menu is open
    if MenuOpen then
        SendNUIMessage({
            action = 'updatePlayerData',
            data = data
        })
    end
end)

RegisterNetEvent('ZGMining:GridsLoaded', function(grids)
    MiningGrids = grids
    Utils.Debug('Mining grids loaded: %d grids', Utils.TableLength(grids))
end)

RegisterNetEvent('ZGMining:MiningStarted', function(rockId, pickaxeData)
    Utils.Debug('Mining started on rock %s', rockId)
    
    -- Start mining loop
    CreateThread(function()
        while CurrentMining and CurrentMining.rockId == rockId do
            Wait(pickaxeData.swingRate or 2000)
            
            if CurrentMining and CurrentMining.rockId == rockId then
                PerformMiningHit(rockId)
            end
        end
    end)
end)

RegisterNetEvent('ZGMining:MiningHit', function(newHealth, maxHealth)
    -- Update rock health display
    if CurrentMining then
        local healthPercent = (newHealth / maxHealth) * 100
        Utils.Debug('Rock health: %d%%', healthPercent)
    end
end)

RegisterNetEvent('ZGMining:MiningComplete', function(rockId, rewards)
    StopMining()
    Framework.Notify(Utils.Locale('mining_complete'), 'success')
    
    -- Show rewards
    if rewards.ore and rewards.amount then
        local itemLabel = Items.All[rewards.ore] and Items.All[rewards.ore].label or rewards.ore
        Framework.Notify(Utils.Locale('item_received', itemLabel, rewards.amount), 'success')
    end
    
    if rewards.xp then
        Framework.Notify(Utils.Locale('xp_gained', rewards.xp), 'info')
    end
end)

RegisterNetEvent('ZGMining:MiningFailed', function(reason)
    StopMining()
    Framework.Notify(Utils.Locale('mining_failed'), 'error')
end)

RegisterNetEvent('ZGMining:PickaxeBroken', function()
    StopMining()
    Framework.Notify(Utils.Locale('pickaxe_broken'), 'error')
end)

RegisterNetEvent('ZGMining:LevelUp', function(newLevel)
    -- Level up effects
    PlaySoundFrontend(-1, 'RANK_UP', 'HUD_AWARDS', false)
    
    -- Screen effect
    StartScreenEffect('MP_CELEBRATION', 3000, false)
end)

RegisterNetEvent('ZGMining:XPGained', function(amount, totalXP, level)
    -- XP notification
    if MenuOpen then
        SendNUIMessage({
            action = 'xpGained',
            amount = amount,
            totalXP = totalXP,
            level = level
        })
    end
end)

-- Exports
exports('OpenMiningMenu', OpenMiningMenu)
exports('GetMiningLevel', function()
    return PlayerData.level or 1
end)
exports('GetMiningXP', function()
    return PlayerData.xp or 0
end)
