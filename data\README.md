# Mining System Data Directory

This directory contains runtime data files for the mining system.

## Files

### grids.json
Contains saved mining grid configurations. This file is automatically created and managed by the system.

Example structure:
```json
{
  "1": {
    "id": 1,
    "name": "Main Mining Area",
    "center": {"x": 2944.0, "y": 2746.0, "z": 43.0},
    "settings": {
      "rows": 5,
      "cols": 5,
      "spacing": 3.0,
      "yaw": 0.0,
      "jitter": 0.1,
      "oreComposition": {
        "copper": 0.4,
        "iron": 0.3,
        "silver": 0.2,
        "gold": 0.1
      }
    },
    "rocks": {
      "Main Mining Area_1": {
        "id": "Main Mining Area_1",
        "coords": {"x": 2941.5, "y": 2743.5, "z": 43.0},
        "oreType": "copper",
        "health": 60,
        "maxHealth": 60,
        "depleted": false
      }
    }
  }
}
```

### backups/
Contains automatic backups of grid configurations and player data.

## Backup System

The system automatically creates backups:
- **Daily**: Player data and grid configurations
- **Weekly**: Complete database dump
- **On Update**: Before any major configuration changes

## Manual Backup

To manually backup your mining data:
1. Copy the entire `data/` directory
2. Export the database tables:
   - `players_mining`
   - `mining_grids`
   - `mining_smelt_jobs`
   - `mining_price_history`

## Restore Process

To restore from backup:
1. Stop the mining resource
2. Restore database tables from backup
3. Replace `data/` directory with backup
4. Restart the mining resource

## File Permissions

Ensure the FiveM server has read/write permissions to this directory for proper operation.

## Maintenance

Regular maintenance tasks:
- Clean old backup files (older than 30 days)
- Monitor file sizes for large servers
- Verify backup integrity periodically

---

**Note**: This directory is automatically managed by the system. Manual editing is not recommended unless you know what you're doing.
