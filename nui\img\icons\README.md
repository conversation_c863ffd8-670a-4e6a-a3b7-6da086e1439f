# Mining System Icons

This directory contains all the icons used in the mining system UI.

## Required Icons (64x64 PNG format)

### Pickaxes
- `pickaxe_wood.png` - Wooden pickaxe icon
- `pickaxe_stone.png` - Stone pickaxe icon
- `pickaxe_iron.png` - Iron pickaxe icon
- `pickaxe_steel.png` - Steel pickaxe icon
- `pickaxe_titanium.png` - Titanium pickaxe icon
- `pickaxe_diamond.png` - Diamond pickaxe icon
- `repair_kit.png` - Repair kit icon

### Ores
- `ore_coal.png` - Coal ore icon
- `ore_copper.png` - Copper ore icon
- `ore_iron.png` - Iron ore icon
- `ore_silver.png` - Silver ore icon
- `ore_gold.png` - Gold ore icon
- `ore_titanium.png` - Titanium ore icon
- `ore_diamond.png` - Diamond shard icon

### Ingots
- `ingot_copper.png` - Copper ingot icon
- `ingot_iron.png` - Iron ingot icon
- `ingot_silver.png` - Silver ingot icon
- `ingot_gold.png` - Gold ingot icon
- `ingot_titanium.png` - Titanium ingot icon
- `gem_diamond.png` - Cut diamond icon
- `coal_coke.png` - Coal coke icon

## Icon Guidelines

- **Format**: PNG with transparency
- **Size**: 64x64 pixels (128x128 for high-res versions)
- **Style**: Flat design with subtle shadows
- **Colors**: Match the dark theme (avoid pure white backgrounds)
- **Consistency**: Use similar lighting and perspective across all icons

## SVG Versions

For scalability, also include SVG versions of all icons in neutral colors that can be styled with CSS.

## Installation

1. Place all PNG files in this directory
2. Ensure filenames match exactly (case-sensitive)
3. Restart the resource to load new icons
4. Test in-game to verify all icons display correctly

## Creating Custom Icons

If you need to create custom icons:
1. Use a consistent art style
2. Maintain the 64x64 pixel size
3. Use PNG format with transparency
4. Test in-game before finalizing
5. Consider creating both light and dark versions
