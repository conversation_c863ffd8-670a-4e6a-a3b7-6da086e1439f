Items = {}

-- Pickaxes
Items.Tools = {
    pickaxe_wood = {
        name = 'pickaxe_wood',
        label = 'Wooden Pickaxe',
        description = 'A basic wooden pickaxe for mining',
        image = 'pickaxe_wood.png',
        model = `prop_tool_pickaxe`,
        swingRate = 2000, -- ms
        damage = 8,
        durabilityMax = 50,
        tier = 1,
        critChance = 0.05,
        levelRequired = 1,
        price = 25,
        weight = 2.0,
        stack = false,
        close = true,
        meta = {
            currentDurability = 50
        }
    },
    pickaxe_stone = {
        name = 'pickaxe_stone',
        label = 'Stone Pickaxe',
        description = 'A sturdy stone pickaxe',
        image = 'pickaxe_stone.png',
        model = `prop_tool_pickaxe`,
        swingRate = 1800,
        damage = 12,
        durabilityMax = 100,
        tier = 2,
        critChance = 0.08,
        levelRequired = 5,
        price = 75,
        weight = 2.5,
        stack = false,
        close = true,
        meta = {
            currentDurability = 100
        }
    },
    pickaxe_iron = {
        name = 'pickaxe_iron',
        label = 'Iron Pickaxe',
        description = 'A reliable iron pickaxe',
        image = 'pickaxe_iron.png',
        model = `prop_tool_pickaxe`,
        swingRate = 1600,
        damage = 18,
        durabilityMax = 200,
        tier = 3,
        critChance = 0.12,
        levelRequired = 10,
        price = 150,
        weight = 3.0,
        stack = false,
        close = true,
        meta = {
            currentDurability = 200
        }
    },
    pickaxe_steel = {
        name = 'pickaxe_steel',
        label = 'Steel Pickaxe',
        description = 'A high-quality steel pickaxe',
        image = 'pickaxe_steel.png',
        model = `prop_tool_pickaxe`,
        swingRate = 1400,
        damage = 25,
        durabilityMax = 350,
        tier = 4,
        critChance = 0.15,
        levelRequired = 20,
        price = 300,
        weight = 3.5,
        stack = false,
        close = true,
        meta = {
            currentDurability = 350
        }
    },
    pickaxe_titanium = {
        name = 'pickaxe_titanium',
        label = 'Titanium Pickaxe',
        description = 'An advanced titanium pickaxe',
        image = 'pickaxe_titanium.png',
        model = `prop_tool_pickaxe`,
        swingRate = 1200,
        damage = 35,
        durabilityMax = 500,
        tier = 5,
        critChance = 0.20,
        levelRequired = 30,
        price = 600,
        weight = 4.0,
        stack = false,
        close = true,
        meta = {
            currentDurability = 500
        }
    },
    pickaxe_diamond = {
        name = 'pickaxe_diamond',
        label = 'Diamond Pickaxe',
        description = 'The ultimate diamond-tipped pickaxe',
        image = 'pickaxe_diamond.png',
        model = `prop_tool_pickaxe`,
        swingRate = 1000,
        damage = 50,
        durabilityMax = 750,
        tier = 6,
        critChance = 0.25,
        levelRequired = 40,
        price = 1200,
        weight = 4.5,
        stack = false,
        close = true,
        meta = {
            currentDurability = 750
        }
    },
    pickaxe_repairkit = {
        name = 'pickaxe_repairkit',
        label = 'Pickaxe Repair Kit',
        description = 'Repairs pickaxe durability',
        image = 'repair_kit.png',
        weight = 0.5,
        stack = true,
        close = true,
        price = 50
    }
}

-- Ores
Items.Ores = {
    ore_coal = {
        name = 'ore_coal',
        label = 'Coal Ore',
        description = 'Raw coal ore, used for fuel',
        image = 'ore_coal.png',
        weight = 0.8,
        stack = true,
        close = true
    },
    ore_copper = {
        name = 'ore_copper',
        label = 'Copper Ore',
        description = 'Raw copper ore',
        image = 'ore_copper.png',
        weight = 1.0,
        stack = true,
        close = true
    },
    ore_iron = {
        name = 'ore_iron',
        label = 'Iron Ore',
        description = 'Raw iron ore',
        image = 'ore_iron.png',
        weight = 1.2,
        stack = true,
        close = true
    },
    ore_silver = {
        name = 'ore_silver',
        label = 'Silver Ore',
        description = 'Raw silver ore',
        image = 'ore_silver.png',
        weight = 1.5,
        stack = true,
        close = true
    },
    ore_gold = {
        name = 'ore_gold',
        label = 'Gold Ore',
        description = 'Raw gold ore',
        image = 'ore_gold.png',
        weight = 2.0,
        stack = true,
        close = true
    },
    ore_titanium = {
        name = 'ore_titanium',
        label = 'Titanium Ore',
        description = 'Raw titanium ore',
        image = 'ore_titanium.png',
        weight = 2.5,
        stack = true,
        close = true
    },
    ore_diamondShard = {
        name = 'ore_diamondShard',
        label = 'Diamond Shard',
        description = 'A raw diamond shard',
        image = 'ore_diamond.png',
        weight = 0.1,
        stack = true,
        close = true
    }
}

-- Ingots and Processed Materials
Items.Ingots = {
    ingot_copper = {
        name = 'ingot_copper',
        label = 'Copper Ingot',
        description = 'Refined copper ingot',
        image = 'ingot_copper.png',
        weight = 1.0,
        stack = true,
        close = true
    },
    ingot_iron = {
        name = 'ingot_iron',
        label = 'Iron Ingot',
        description = 'Refined iron ingot',
        image = 'ingot_iron.png',
        weight = 1.2,
        stack = true,
        close = true
    },
    ingot_silver = {
        name = 'ingot_silver',
        label = 'Silver Ingot',
        description = 'Refined silver ingot',
        image = 'ingot_silver.png',
        weight = 1.5,
        stack = true,
        close = true
    },
    ingot_gold = {
        name = 'ingot_gold',
        label = 'Gold Ingot',
        description = 'Refined gold ingot',
        image = 'ingot_gold.png',
        weight = 2.0,
        stack = true,
        close = true
    },
    ingot_titanium = {
        name = 'ingot_titanium',
        label = 'Titanium Ingot',
        description = 'Refined titanium ingot',
        image = 'ingot_titanium.png',
        weight = 2.5,
        stack = true,
        close = true
    },
    gem_diamond = {
        name = 'gem_diamond',
        label = 'Cut Diamond',
        description = 'A perfectly cut diamond',
        image = 'gem_diamond.png',
        weight = 0.1,
        stack = true,
        close = true
    },
    coal_coke = {
        name = 'coal_coke',
        label = 'Coal Coke',
        description = 'High-grade fuel for smelting',
        image = 'coal_coke.png',
        weight = 0.5,
        stack = true,
        close = true
    }
}

-- Combine all items
Items.All = {}
for category, items in pairs(Items) do
    if category ~= 'All' then
        for itemName, itemData in pairs(items) do
            Items.All[itemName] = itemData
        end
    end
end
