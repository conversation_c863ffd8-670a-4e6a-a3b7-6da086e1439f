<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ZG Mining System</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div id="app" class="app-container" style="display: none;">
        <!-- Main Menu -->
        <div id="mainMenu" class="main-menu">
            <div class="menu-header">
                <h1 class="menu-title">
                    <i class="fas fa-mountain"></i>
                    The Mines
                </h1>
                <button class="close-btn" onclick="closeMenu()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="menu-content">
                <!-- Left Navigation -->
                <div class="nav-section">
                    <div class="nav-card active" data-tab="level" onclick="switchTab('level')">
                        <div class="nav-card-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="nav-card-content">
                            <h3>Mining Level</h3>
                            <p id="playerLevelText">Level 1/50</p>
                        </div>
                    </div>
                    
                    <div class="nav-card" data-tab="stats" onclick="switchTab('stats')">
                        <div class="nav-card-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <div class="nav-card-content">
                            <h3>Lifetime Statistics</h3>
                            <p>View your progress</p>
                        </div>
                    </div>
                    
                    <div class="nav-card" data-tab="leaderboards" onclick="switchTab('leaderboards')">
                        <div class="nav-card-icon">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <div class="nav-card-content">
                            <h3>The Leaderboards</h3>
                            <p>Top miners</p>
                        </div>
                    </div>
                    
                    <div class="nav-card" data-tab="shop" onclick="switchTab('shop')">
                        <div class="nav-card-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="nav-card-content">
                            <h3>The Shop</h3>
                            <p>Buy equipment</p>
                        </div>
                    </div>
                    
                    <div class="nav-card" data-tab="pawn" onclick="switchTab('pawn')">
                        <div class="nav-card-icon">
                            <i class="fas fa-coins"></i>
                        </div>
                        <div class="nav-card-content">
                            <h3>Pawn Shop</h3>
                            <p>Sell your ores</p>
                        </div>
                    </div>
                    
                    <div class="nav-card" data-tab="smelting" onclick="switchTab('smelting')">
                        <div class="nav-card-icon">
                            <i class="fas fa-fire"></i>
                        </div>
                        <div class="nav-card-content">
                            <h3>Smelting</h3>
                            <p>Process ores</p>
                        </div>
                    </div>
                    
                    <div class="nav-card admin-only" data-tab="admin" onclick="switchTab('admin')" style="display: none;">
                        <div class="nav-card-icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        <div class="nav-card-content">
                            <h3>Admin Panel</h3>
                            <p>Manage mining</p>
                        </div>
                    </div>
                </div>
                
                <!-- Main Content Area -->
                <div class="content-section">
                    <!-- Level Tab -->
                    <div id="levelTab" class="tab-content active">
                        <div class="content-header">
                            <h2>Mining Progress</h2>
                        </div>
                        
                        <div class="level-display">
                            <div class="level-circle">
                                <div class="level-number" id="currentLevel">1</div>
                                <div class="level-label">Level</div>
                            </div>
                            
                            <div class="xp-info">
                                <div class="xp-bar-container">
                                    <div class="xp-bar">
                                        <div class="xp-fill" id="xpProgress" style="width: 0%"></div>
                                    </div>
                                    <div class="xp-text">
                                        <span id="currentXP">0</span> / <span id="nextLevelXP">100</span> XP
                                    </div>
                                </div>
                                
                                <div class="level-perks">
                                    <h4>Level Perks</h4>
                                    <div class="perk-list" id="levelPerks">
                                        <!-- Perks will be populated by JS -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Stats Tab -->
                    <div id="statsTab" class="tab-content">
                        <div class="content-header">
                            <h2>Lifetime Statistics</h2>
                        </div>
                        
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-gem"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-value" id="oresMined">0</div>
                                    <div class="stat-label">Ores Mined</div>
                                </div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-value" id="timeMining">0h</div>
                                    <div class="stat-label">Time Mining</div>
                                </div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-value" id="moneyEarned">$0</div>
                                    <div class="stat-label">Money Earned</div>
                                </div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-fire"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-value" id="itemsSmelted">0</div>
                                    <div class="stat-label">Items Smelted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Leaderboards Tab -->
                    <div id="leaderboardsTab" class="tab-content">
                        <div class="content-header">
                            <h2>The Leaderboards</h2>
                        </div>
                        
                        <div class="leaderboard-container">
                            <div class="leaderboard-tabs">
                                <button class="lb-tab active" onclick="switchLeaderboard('miners')">Top Miners</button>
                                <button class="lb-tab" onclick="switchLeaderboard('earners')">Top Earners</button>
                                <button class="lb-tab" onclick="switchLeaderboard('levels')">Highest Level</button>
                            </div>
                            
                            <div class="leaderboard-list" id="leaderboardList">
                                <!-- Leaderboard entries will be populated by JS -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- Shop Tab -->
                    <div id="shopTab" class="tab-content">
                        <div class="content-header">
                            <h2>The Shop</h2>
                        </div>
                        
                        <div class="shop-container">
                            <div class="shop-categories">
                                <button class="shop-cat active" onclick="switchShopCategory('pickaxes')">Pickaxes</button>
                                <button class="shop-cat" onclick="switchShopCategory('repairs')">Repair Kits</button>
                            </div>
                            
                            <div class="shop-items" id="shopItems">
                                <!-- Shop items will be populated by JS -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- Pawn Shop Tab -->
                    <div id="pawnTab" class="tab-content">
                        <div class="content-header">
                            <h2>Pawn Shop</h2>
                            <p>Sell your ores and ingots for cash</p>
                        </div>
                        
                        <div class="pawn-container">
                            <div class="inventory-section">
                                <h3>Your Inventory</h3>
                                <div class="inventory-list" id="inventoryList">
                                    <!-- Inventory items will be populated by JS -->
                                </div>
                            </div>
                            
                            <div class="sell-section">
                                <h3>Sell Cart</h3>
                                <div class="sell-cart" id="sellCart">
                                    <div class="empty-cart">
                                        <i class="fas fa-shopping-cart"></i>
                                        <p>Add items to sell</p>
                                    </div>
                                </div>
                                
                                <div class="sell-total">
                                    <div class="total-line">
                                        <span>Subtotal:</span>
                                        <span id="sellSubtotal">$0</span>
                                    </div>
                                    <div class="total-line">
                                        <span>Tax (5%):</span>
                                        <span id="sellTax">$0</span>
                                    </div>
                                    <div class="total-line total">
                                        <span>Total:</span>
                                        <span id="sellTotal">$0</span>
                                    </div>
                                </div>
                                
                                <button class="sell-btn" id="sellButton" onclick="sellItems()" disabled>
                                    <i class="fas fa-hand-holding-usd"></i>
                                    Sell Items
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Smelting Tab -->
                    <div id="smeltingTab" class="tab-content">
                        <div class="content-header">
                            <h2>Smelting</h2>
                            <p>Process raw ores into valuable ingots</p>
                        </div>
                        
                        <div class="smelting-container">
                            <div class="recipes-section">
                                <h3>Available Recipes</h3>
                                <div class="recipe-list" id="recipeList">
                                    <!-- Recipes will be populated by JS -->
                                </div>
                            </div>
                            
                            <div class="queue-section">
                                <h3>Smelting Queue</h3>
                                <div class="queue-list" id="queueList">
                                    <div class="empty-queue">
                                        <i class="fas fa-fire"></i>
                                        <p>No active smelting jobs</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Admin Tab -->
                    <div id="adminTab" class="tab-content">
                        <div class="content-header">
                            <h2>Admin Panel</h2>
                        </div>
                        
                        <div class="admin-container">
                            <div class="admin-section">
                                <h3>Grid Placement</h3>
                                <button class="admin-btn" onclick="openPlacement()">
                                    <i class="fas fa-map"></i>
                                    Open 3D Placement
                                </button>
                            </div>
                            
                            <div class="admin-section">
                                <h3>Server Management</h3>
                                <button class="admin-btn" onclick="reloadConfig()">
                                    <i class="fas fa-sync"></i>
                                    Reload Config
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Right Sidebar -->
                <div class="sidebar-section">
                    <div class="sidebar-card">
                        <h3>Next Level</h3>
                        <div class="next-level-info">
                            <div class="level-progress-circle">
                                <svg class="progress-ring" width="80" height="80">
                                    <circle class="progress-ring-circle" cx="40" cy="40" r="35" stroke-width="6"></circle>
                                </svg>
                                <div class="progress-text" id="progressPercent">0%</div>
                            </div>
                            <div class="progress-details">
                                <p><strong id="xpRemaining">100</strong> XP remaining</p>
                                <p>Level <strong id="nextLevel">2</strong></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="sidebar-card">
                        <h3>Quick Stats</h3>
                        <div class="quick-stats">
                            <div class="quick-stat">
                                <span class="stat-label">Current Level</span>
                                <span class="stat-value" id="sidebarLevel">1</span>
                            </div>
                            <div class="quick-stat">
                                <span class="stat-label">Total XP</span>
                                <span class="stat-value" id="sidebarXP">0</span>
                            </div>
                            <div class="quick-stat">
                                <span class="stat-label">Efficiency</span>
                                <span class="stat-value" id="sidebarEfficiency">0%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Placement UI -->
        <div id="placementUI" class="placement-ui" style="display: none;">
            <div class="placement-header">
                <h2>3D Grid Placement</h2>
                <button class="close-btn" onclick="closePlacement()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="placement-controls">
                <div class="control-group">
                    <label>Grid Size</label>
                    <div class="control-row">
                        <input type="range" id="gridRows" min="1" max="20" value="5" oninput="updatePlacement()">
                        <span id="gridRowsValue">5</span> x
                        <input type="range" id="gridCols" min="1" max="20" value="5" oninput="updatePlacement()">
                        <span id="gridColsValue">5</span>
                    </div>
                </div>

                <div class="control-group">
                    <label>Spacing</label>
                    <input type="range" id="gridSpacing" min="1" max="10" step="0.5" value="3" oninput="updatePlacement()">
                    <span id="gridSpacingValue">3.0m</span>
                </div>

                <div class="control-group">
                    <label>Rotation</label>
                    <input type="range" id="gridYaw" min="0" max="360" value="0" oninput="updatePlacement()">
                    <span id="gridYawValue">0°</span>
                </div>

                <div class="control-group">
                    <label>Jitter</label>
                    <input type="range" id="gridJitter" min="0" max="1" step="0.1" value="0.1" oninput="updatePlacement()">
                    <span id="gridJitterValue">10%</span>
                </div>

                <div class="ore-composition">
                    <h4>Ore Composition</h4>
                    <div class="ore-sliders">
                        <div class="ore-slider">
                            <label>Copper</label>
                            <input type="range" id="oreCopper" min="0" max="1" step="0.1" value="0.4" oninput="updateOreComposition()">
                            <span id="oreCopperValue">40%</span>
                        </div>
                        <div class="ore-slider">
                            <label>Iron</label>
                            <input type="range" id="oreIron" min="0" max="1" step="0.1" value="0.3" oninput="updateOreComposition()">
                            <span id="oreIronValue">30%</span>
                        </div>
                        <div class="ore-slider">
                            <label>Silver</label>
                            <input type="range" id="oreSilver" min="0" max="1" step="0.1" value="0.2" oninput="updateOreComposition()">
                            <span id="oreSilverValue">20%</span>
                        </div>
                        <div class="ore-slider">
                            <label>Gold</label>
                            <input type="range" id="oreGold" min="0" max="1" step="0.1" value="0.1" oninput="updateOreComposition()">
                            <span id="oreGoldValue">10%</span>
                        </div>
                    </div>
                </div>

                <div class="placement-actions">
                    <input type="text" id="gridName" placeholder="Grid Name" value="">
                    <button class="bake-btn" onclick="bakeGrid()">
                        <i class="fas fa-hammer"></i>
                        Bake Grid
                    </button>
                    <button class="clear-btn" onclick="clearPreview()">
                        <i class="fas fa-trash"></i>
                        Clear Preview
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Notifications -->
        <div id="notifications" class="notifications-container"></div>
        
        <!-- Loading Overlay -->
        <div id="loadingOverlay" class="loading-overlay">
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
                <p>Loading...</p>
            </div>
        </div>
    </div>
    
    <script src="app.js"></script>
</body>
</html>
