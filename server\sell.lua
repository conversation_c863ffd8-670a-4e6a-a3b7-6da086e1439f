-- ZG Mining System - Selling System

local SellPrices = {}
local PriceHistory = {}

-- Initialize selling system
local function InitializeSelling()
    -- Load base prices
    for item, price in pairs(Config.Economy.basePrices) do
        SellPrices[item] = price
    end
    
    -- Load price history if dynamic pricing is enabled
    if Config.Economy.dynamicPricing.enabled then
        LoadPriceHistory()
    end
    
    Utils.Log('info', 'Selling system initialized')
end

-- Dynamic Pricing System
local function LoadPriceHistory()
    MySQL.query([[
        CREATE TABLE IF NOT EXISTS `mining_price_history` (
            `id` INT(11) NOT NULL AUTO_INCREMENT,
            `item` VARCHAR(50) NOT NULL,
            `price` DECIMAL(10,2) NOT NULL,
            `quantity_sold` INT(11) NOT NULL DEFAULT 0,
            `timestamp` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            INDEX `idx_item_timestamp` (`item`, `timestamp`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ]])
    
    -- Load recent price history
    local hoursBack = Config.Economy.dynamicPricing.window
    MySQL.query([[
        SELECT item, AVG(price) as avg_price, SUM(quantity_sold) as total_sold 
        FROM mining_price_history 
        WHERE timestamp >= DATE_SUB(NOW(), INTERVAL ? HOUR)
        GROUP BY item
    ]], {hoursBack}, function(results)
        if results then
            for _, row in pairs(results) do
                if not PriceHistory[row.item] then
                    PriceHistory[row.item] = {}
                end
                PriceHistory[row.item].avgPrice = row.avg_price
                PriceHistory[row.item].totalSold = row.total_sold
            end
        end
    end)
end

local function UpdateDynamicPrice(item, quantitySold)
    if not Config.Economy.dynamicPricing.enabled then return end
    
    local basePrice = Config.Economy.basePrices[item]
    if not basePrice then return end
    
    -- Simple supply/demand calculation
    local history = PriceHistory[item] or {}
    local recentSales = history.totalSold or 0
    
    -- More sales = lower price, fewer sales = higher price
    local demandFactor = 1.0
    if recentSales > 100 then
        demandFactor = 0.8 -- High supply, lower price
    elseif recentSales < 20 then
        demandFactor = 1.2 -- Low supply, higher price
    end
    
    -- Apply fluctuation limits
    local maxFluctuation = Config.Economy.dynamicPricing.maxFluctuation
    demandFactor = Utils.Clamp(demandFactor, 1 - maxFluctuation, 1 + maxFluctuation)
    
    SellPrices[item] = Utils.Round(basePrice * demandFactor, 2)
    
    -- Record price change
    MySQL.insert('INSERT INTO mining_price_history (item, price, quantity_sold) VALUES (?, ?, ?)', {
        item, SellPrices[item], quantitySold
    })
end

-- Get current sell price for an item
local function GetSellPrice(source, item, quantity)
    local basePrice = SellPrices[item] or Config.Economy.basePrices[item] or 0
    if basePrice == 0 then return 0 end
    
    local playerData = exports['ZGMining']:GetPlayerMiningData(source)
    local level = playerData and playerData.level or 1
    
    -- Calculate job multiplier
    local jobMultiplier = 1.0
    if Config.Economy.jobBonus.enabled then
        local playerJob = Framework.GetJob(source)
        if playerJob and Utils.TableContains(Config.Economy.jobBonus.jobs, playerJob) then
            jobMultiplier = Config.Economy.jobBonus.multiplier
        end
    end
    
    local finalPrice = Utils.CalculateSellPrice(item, basePrice, level, jobMultiplier)
    return finalPrice * quantity
end

-- Sell items
RegisterNetEvent('ZGMining:SellItems', function(items)
    local source = source
    local playerData = exports['ZGMining']:GetPlayerMiningData(source)
    
    if not playerData then
        Framework.Notify(source, Utils.Locale('error_generic'), 'error')
        return
    end
    
    local totalValue = 0
    local sellDetails = {}
    
    -- Validate and calculate total
    for _, sellItem in pairs(items) do
        local itemName = sellItem.item
        local quantity = sellItem.quantity or 1
        
        -- Check if player has the item
        if not Framework.HasItem(source, itemName, quantity) then
            Framework.Notify(source, Utils.Locale('insufficient_items'), 'error')
            return
        end
        
        -- Check if item is sellable
        if not Config.Economy.basePrices[itemName] then
            Framework.Notify(source, Utils.Locale('error_invalid_item'), 'error')
            return
        end
        
        local itemValue = GetSellPrice(source, itemName, quantity)
        totalValue = totalValue + itemValue
        
        table.insert(sellDetails, {
            item = itemName,
            quantity = quantity,
            unitPrice = itemValue / quantity,
            totalPrice = itemValue
        })
    end
    
    if totalValue <= 0 then
        Framework.Notify(source, Utils.Locale('error_generic'), 'error')
        return
    end
    
    -- Remove items and add money
    local success = true
    for _, sellItem in pairs(items) do
        if not Framework.RemoveItem(source, sellItem.item, sellItem.quantity) then
            success = false
            break
        end
    end
    
    if not success then
        -- Rollback - add items back
        for _, sellItem in pairs(items) do
            Framework.AddItem(source, sellItem.item, sellItem.quantity)
        end
        Framework.Notify(source, Utils.Locale('error_generic'), 'error')
        return
    end
    
    -- Add money
    if Framework.AddMoney(source, 'cash', totalValue, 'Mining sales') then
        -- Update player stats
        playerData.stats.money_earned = playerData.stats.money_earned + totalValue
        
        -- Update dynamic pricing
        for _, sellItem in pairs(items) do
            UpdateDynamicPrice(sellItem.item, sellItem.quantity)
        end
        
        -- Notify success
        Framework.Notify(source, Utils.Locale('sale_successful') .. ' ' .. Utils.FormatMoney(totalValue), 'success')
        
        -- Send sell details to client
        TriggerClientEvent('ZGMining:SellComplete', source, {
            total = totalValue,
            items = sellDetails
        })
        
        -- Trigger sell event
        TriggerEvent('ZGMining:ItemsSold', source, items, totalValue)
        
        Utils.Log('info', 'Player %s sold items for $%s', Framework.GetPlayerIdentifier(source), totalValue)
    else
        -- Rollback - add items back
        for _, sellItem in pairs(items) do
            Framework.AddItem(source, sellItem.item, sellItem.quantity)
        end
        Framework.Notify(source, Utils.Locale('error_generic'), 'error')
    end
end)

-- Get sell prices for client
RegisterNetEvent('ZGMining:GetSellPrices', function()
    local source = source
    local prices = {}
    
    for item, basePrice in pairs(Config.Economy.basePrices) do
        prices[item] = {
            basePrice = basePrice,
            currentPrice = GetSellPrice(source, item, 1),
            item = Items.All[item]
        }
    end
    
    TriggerClientEvent('ZGMining:ReceiveSellPrices', source, prices)
end)

-- Get market data
RegisterNetEvent('ZGMining:GetMarketData', function()
    local source = source
    local marketData = {}
    
    for item, basePrice in pairs(Config.Economy.basePrices) do
        local currentPrice = SellPrices[item] or basePrice
        local priceChange = ((currentPrice - basePrice) / basePrice) * 100
        
        marketData[item] = {
            basePrice = basePrice,
            currentPrice = currentPrice,
            priceChange = priceChange,
            trend = priceChange > 0 and 'up' or (priceChange < 0 and 'down' or 'stable'),
            history = PriceHistory[item] or {}
        }
    end
    
    TriggerClientEvent('ZGMining:ReceiveMarketData', source, marketData)
end)

-- Utility function to check if table contains value
function Utils.TableContains(table, value)
    for _, v in pairs(table) do
        if v == value then
            return true
        end
    end
    return false
end

-- Price update thread for dynamic pricing
if Config.Economy.dynamicPricing.enabled then
    CreateThread(function()
        while true do
            Wait(300000) -- Update every 5 minutes
            
            -- Recalculate prices based on recent sales
            for item, _ in pairs(Config.Economy.basePrices) do
                UpdateDynamicPrice(item, 0) -- Update without new sales
            end
        end
    end)
end

-- Initialize on resource start
CreateThread(function()
    Wait(1000) -- Wait for framework to initialize
    InitializeSelling()
end)

-- Exports
exports('GetSellPrice', GetSellPrice)
exports('GetCurrentPrices', function()
    return SellPrices
end)
exports('AddSellModifier', function(callback)
    -- Allow other resources to modify sell prices
    -- This would be implemented as a hook system
end)
