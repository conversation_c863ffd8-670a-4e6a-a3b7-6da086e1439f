fx_version 'cerulean'
game 'gta5'
lua54 'yes'

name 'ZG Mining System'
description 'Advanced mining system with 3D placement, smelting, and economy'
author 'ZG Development'
version '1.0.0'

-- Escrow ignore for configs
escrow_ignore {
    'config/*.lua',
    'README.md',
    'INSTALL.md'
}

-- Shared scripts
shared_scripts {
    'shared/utils.lua',
    'shared/bridges.lua',
    'config/config.lua',
    'config/items.lua',
    'config/recipes.lua',
    'config/locales.lua'
}

-- Server scripts
server_scripts {
    '@oxmysql/lib/MySQL.lua',
    'server/framework.lua',
    'server/main.lua',
    'server/sell.lua',
    'server/smelt.lua'
}

-- Client scripts
client_scripts {
    'client/main.lua',
    'client/placement.lua',
    'client/rocks.lua',
    'client/ui.lua',
    'client/targets.lua'
}

-- UI files
ui_page 'nui/index.html'

files {
    'nui/index.html',
    'nui/app.js',
    'nui/styles.css',
    'nui/fonts/*.ttf',
    'nui/fonts/*.woff',
    'nui/fonts/*.woff2',
    'nui/img/icons/*.png',
    'nui/img/icons/*.svg',
    'nui/sounds/*.ogg',
    'assets/models/pickaxes/*.ydr',
    'assets/models/pickaxes/*.ytd',
    'assets/props/ghost_rock.yft',
    'assets/props/ghost_rock.ytd'
}

-- Dependencies
dependencies {
    'oxmysql'
}

-- Exports
exports {
    'OpenMiningMenu',
    'OpenPlacementUI',
    'GetMiningLevel',
    'GetMiningXP'
}

server_exports {
    'AddSmeltRecipe',
    'RegisterOreType',
    'AddSellModifier'
}
