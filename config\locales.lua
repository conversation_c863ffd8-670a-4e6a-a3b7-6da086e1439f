Locales = {}

Locales['en'] = {
    -- General
    ['mining_system'] = 'Mining System',
    ['level'] = 'Level',
    ['xp'] = 'XP',
    ['next_level'] = 'Next Level',
    ['remainder'] = 'Remainder',
    ['close'] = 'Close',
    ['confirm'] = 'Confirm',
    ['cancel'] = 'Cancel',
    ['loading'] = 'Loading...',
    ['error'] = 'Error',
    ['success'] = 'Success',
    ['warning'] = 'Warning',
    ['info'] = 'Info',
    
    -- Menu Sections
    ['mining_level'] = 'Mining Level',
    ['lifetime_stats'] = 'Lifetime Statistics',
    ['leaderboards'] = 'The Leaderboards',
    ['the_shop'] = 'The Shop',
    ['pawn_shop'] = 'Pawn Shop',
    ['smelting'] = 'Smelting',
    ['admin_panel'] = 'Admin Panel',
    
    -- Mining Actions
    ['start_mining'] = 'Start Mining',
    ['mining_in_progress'] = 'Mining...',
    ['mining_complete'] = 'Mining Complete!',
    ['rock_depleted'] = 'Rock depleted',
    ['pickaxe_broken'] = 'Your pickaxe has broken!',
    ['repair_pickaxe'] = 'Repair Pickaxe',
    ['insufficient_durability'] = 'Pickaxe durability too low',
    ['mining_failed'] = 'Mining failed',
    ['skill_check_failed'] = 'Skill check failed',
    
    -- Inventory
    ['inventory_full'] = 'Inventory is full',
    ['item_received'] = 'Received %s x%d',
    ['item_removed'] = 'Removed %s x%d',
    ['insufficient_items'] = 'Insufficient items',
    
    -- XP and Leveling
    ['xp_gained'] = '+%d XP',
    ['level_up'] = 'Level Up! You are now level %d',
    ['level_requirement'] = 'Requires level %d',
    ['current_level'] = 'Current Level: %d',
    ['total_xp'] = 'Total XP: %s',
    ['xp_to_next'] = 'XP to next level: %s',
    
    -- Shop
    ['purchase'] = 'Purchase',
    ['sell'] = 'Sell',
    ['price'] = 'Price',
    ['quantity'] = 'Quantity',
    ['total'] = 'Total',
    ['insufficient_money'] = 'Insufficient money',
    ['purchase_successful'] = 'Purchase successful',
    ['sale_successful'] = 'Sale successful',
    ['item_price'] = '$%s each',
    ['bulk_discount'] = 'Bulk discount: %d%%',
    
    -- Smelting
    ['start_smelting'] = 'Start Smelting',
    ['smelting_in_progress'] = 'Smelting (%d/%d) - %d%%',
    ['smelting_complete'] = 'Smelting Complete!',
    ['smelting_queue'] = 'Smelting Queue',
    ['queue_position'] = 'Queue Position: %d',
    ['estimated_time'] = 'Estimated Time: %s',
    ['cancel_smelt'] = 'Cancel Smelting',
    ['smelt_cancelled'] = 'Smelting cancelled',
    ['insufficient_fuel'] = 'Insufficient fuel',
    ['furnace_busy'] = 'Furnace is busy',
    ['recipe_unlocked'] = 'Recipe unlocked!',
    
    -- Admin
    ['admin_only'] = 'Admin only',
    ['grid_placed'] = 'Mining grid placed successfully',
    ['grid_removed'] = 'Mining grid removed',
    ['invalid_location'] = 'Invalid location',
    ['placement_mode'] = 'Placement Mode',
    ['preview_mode'] = 'Preview Mode',
    ['bake_grid'] = 'Bake Grid',
    ['clear_preview'] = 'Clear Preview',
    ['grid_settings'] = 'Grid Settings',
    ['ore_composition'] = 'Ore Composition',
    
    -- PED Interactions
    ['talk_to_foreman'] = 'Talk to Mining Foreman',
    ['talk_to_seller'] = 'Talk to Ore Buyer',
    ['talk_to_smelter'] = 'Talk to Smelter',
    ['foreman_greeting'] = 'Welcome to the mines! Need equipment or repairs?',
    ['seller_greeting'] = 'Looking to sell your hard-earned ores?',
    ['smelter_greeting'] = 'Ready to turn those ores into valuable ingots?',
    
    -- Statistics
    ['ores_mined'] = 'Ores Mined',
    ['time_mining'] = 'Time Mining',
    ['money_earned'] = 'Money Earned',
    ['items_smelted'] = 'Items Smelted',
    ['pickaxes_broken'] = 'Pickaxes Broken',
    ['highest_level'] = 'Highest Level',
    ['favorite_ore'] = 'Favorite Ore',
    ['efficiency_rating'] = 'Efficiency Rating',
    
    -- Leaderboards
    ['top_miners'] = 'Top Miners',
    ['top_earners'] = 'Top Earners',
    ['most_experienced'] = 'Most Experienced',
    ['rank'] = 'Rank',
    ['player'] = 'Player',
    ['score'] = 'Score',
    
    -- Notifications
    ['config_reloaded'] = 'Mining configuration reloaded',
    ['permission_denied'] = 'Permission denied',
    ['command_usage'] = 'Usage: %s',
    ['player_not_found'] = 'Player not found',
    ['invalid_amount'] = 'Invalid amount',
    
    -- Keybinds
    ['open_mining_menu'] = 'Open Mining Menu',
    ['interact'] = 'Interact',
    ['cancel_action'] = 'Cancel Action',
    
    -- Durability
    ['durability'] = 'Durability',
    ['durability_low'] = 'Durability Low',
    ['durability_critical'] = 'Durability Critical',
    ['repair_cost'] = 'Repair Cost: $%d',
    ['repair_successful'] = 'Pickaxe repaired successfully',
    ['repair_failed'] = 'Repair failed',
    
    -- Economy
    ['market_price'] = 'Market Price',
    ['base_price'] = 'Base Price',
    ['current_demand'] = 'Current Demand',
    ['price_trend'] = 'Price Trend',
    ['tax_applied'] = 'Tax Applied: %d%%',
    ['job_bonus'] = 'Job Bonus: +%d%%',
    
    -- Time Formats
    ['seconds'] = '%ds',
    ['minutes'] = '%dm',
    ['hours'] = '%dh',
    ['days'] = '%dd',
    ['time_format'] = '%dh %dm %ds',
    
    -- Errors
    ['error_generic'] = 'An error occurred',
    ['error_database'] = 'Database error',
    ['error_framework'] = 'Framework error',
    ['error_inventory'] = 'Inventory error',
    ['error_permission'] = 'Permission error',
    ['error_cooldown'] = 'Action on cooldown',
    ['error_distance'] = 'Too far away',
    ['error_invalid_item'] = 'Invalid item',
    ['error_invalid_recipe'] = 'Invalid recipe',
    
    -- Tutorial
    ['tutorial_welcome'] = 'Welcome to the Mining System!',
    ['tutorial_step1'] = 'Visit the Mining Foreman to get your first pickaxe',
    ['tutorial_step2'] = 'Find mining rocks and start mining',
    ['tutorial_step3'] = 'Smelt your ores into valuable ingots',
    ['tutorial_step4'] = 'Sell your products for profit',
    ['tutorial_complete'] = 'Tutorial complete! Happy mining!',
    
    -- Commands
    ['command_miningmenu'] = 'Open the mining menu',
    ['command_miningplace'] = 'Open placement mode (Admin)',
    ['command_miningreload'] = 'Reload mining configuration (Admin)',
    ['command_mininggive'] = 'Give mining items (Admin)',
}
