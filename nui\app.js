// ZG Mining System - NUI Application

class MiningApp {
    constructor() {
        this.currentTab = 'level';
        this.playerData = {};
        this.sellPrices = {};
        this.smeltJobs = [];
        this.leaderboards = {};
        this.marketData = {};
        this.sellCart = [];
        this.isPlacementMode = false;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.hideApp();
        
        // Listen for NUI messages
        window.addEventListener('message', (event) => {
            this.handleMessage(event.data);
        });
        
        // ESC key to close
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape') {
                this.closeMenu();
            }
        });
    }
    
    setupEventListeners() {
        // Tab switching
        document.querySelectorAll('.nav-card').forEach(card => {
            card.addEventListener('click', () => {
                const tab = card.dataset.tab;
                this.switchTab(tab);
            });
        });
        
        // Close button
        document.querySelector('.close-btn').addEventListener('click', () => {
            this.closeMenu();
        });
    }
    
    handleMessage(data) {
        switch (data.action) {
            case 'openMenu':
                this.openMenu(data.playerData, data.config);
                break;
            case 'closeMenu':
                this.closeMenu();
                break;
            case 'updatePlayerData':
                this.updatePlayerData(data.data);
                break;
            case 'updateSellPrices':
                this.updateSellPrices(data.data);
                break;
            case 'updateSmeltJobs':
                this.updateSmeltJobs(data.data);
                break;
            case 'updateLeaderboards':
                this.updateLeaderboards(data.data);
                break;
            case 'updateMarketData':
                this.updateMarketData(data.data);
                break;
            case 'notification':
                this.showNotification(data.data.message, data.data.type, data.data.duration);
                break;
            case 'switchTab':
                this.switchTab(data.tab);
                break;
            case 'openPlacement':
                this.openPlacement(data.data, data.config);
                break;
            case 'closePlacement':
                this.closePlacement();
                break;
            case 'xpGained':
                this.showXPGain(data.amount);
                break;
            case 'levelUp':
                this.showLevelUp(data.level);
                break;
        }
    }
    
    openMenu(playerData, config) {
        this.playerData = playerData || {};
        this.config = config || {};

        this.showApp();
        this.updateUI();
        this.requestData();

        // Enable cursor and focus
        SetNuiFocus(true, true);
        SetNuiFocusKeepInput(false);
    }
    
    closeMenu() {
        this.hideApp();
        this.sendNUIMessage('closeMenu');

        // Enable cursor and focus
        SetNuiFocus(false, false);
        SetNuiFocusKeepInput(false);
    }
    
    showApp() {
        document.getElementById('app').style.display = 'flex';
        document.getElementById('loadingOverlay').style.display = 'none';
    }
    
    hideApp() {
        document.getElementById('app').style.display = 'none';
    }
    
    switchTab(tabName) {
        // Update navigation
        document.querySelectorAll('.nav-card').forEach(card => {
            card.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        
        // Update content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}Tab`).classList.add('active');
        
        this.currentTab = tabName;
        
        // Load tab-specific data
        this.loadTabData(tabName);
    }
    
    loadTabData(tabName) {
        switch (tabName) {
            case 'stats':
                this.updateStatsTab();
                break;
            case 'leaderboards':
                this.requestData('leaderboards');
                break;
            case 'shop':
                this.updateShopTab();
                break;
            case 'pawn':
                this.updatePawnTab();
                break;
            case 'smelting':
                this.updateSmeltingTab();
                break;
        }
    }
    
    updateUI() {
        this.updateLevelDisplay();
        this.updateSidebar();
    }
    
    updateLevelDisplay() {
        const level = this.playerData.level || 1;
        const xp = this.playerData.xp || 0;
        
        // Calculate XP progress
        const currentLevelXP = this.calculateXPForLevel(level);
        const nextLevelXP = this.calculateXPForLevel(level + 1);
        const progressXP = xp - currentLevelXP;
        const requiredXP = nextLevelXP - currentLevelXP;
        const progressPercent = Math.max(0, Math.min(100, (progressXP / requiredXP) * 100));
        
        // Update level display
        document.getElementById('currentLevel').textContent = level;
        document.getElementById('playerLevelText').textContent = `Level ${level}/${this.config.maxLevel || 50}`;
        document.getElementById('currentXP').textContent = this.formatNumber(progressXP);
        document.getElementById('nextLevelXP').textContent = this.formatNumber(requiredXP);
        document.getElementById('xpProgress').style.width = `${progressPercent}%`;
        
        // Update level perks
        this.updateLevelPerks(level);
    }
    
    updateLevelPerks(level) {
        const perksContainer = document.getElementById('levelPerks');
        perksContainer.innerHTML = '';
        
        const perks = [
            { level: 5, icon: 'fas fa-hammer', text: 'Unlock Iron Pickaxe' },
            { level: 10, icon: 'fas fa-gem', text: 'Unlock Silver Mining' },
            { level: 15, icon: 'fas fa-percentage', text: '+5% Sell Bonus' },
            { level: 20, icon: 'fas fa-coins', text: 'Unlock Gold Mining' },
            { level: 25, icon: 'fas fa-fire', text: 'Faster Smelting' },
            { level: 30, icon: 'fas fa-mountain', text: 'Unlock Titanium Mining' },
            { level: 40, icon: 'fas fa-diamond', text: 'Unlock Diamond Mining' },
            { level: 50, icon: 'fas fa-crown', text: 'Master Miner' }
        ];
        
        perks.forEach(perk => {
            const isUnlocked = level >= perk.level;
            const perkElement = document.createElement('div');
            perkElement.className = `perk-item ${isUnlocked ? 'unlocked' : 'locked'}`;
            perkElement.innerHTML = `
                <div class="perk-icon">
                    <i class="${perk.icon}"></i>
                </div>
                <div class="perk-text">
                    Level ${perk.level}: ${perk.text}
                </div>
            `;
            perksContainer.appendChild(perkElement);
        });
    }
    
    updateSidebar() {
        const level = this.playerData.level || 1;
        const xp = this.playerData.xp || 0;
        
        // Calculate progress for next level
        const currentLevelXP = this.calculateXPForLevel(level);
        const nextLevelXP = this.calculateXPForLevel(level + 1);
        const progressXP = xp - currentLevelXP;
        const requiredXP = nextLevelXP - currentLevelXP;
        const progressPercent = Math.max(0, Math.min(100, (progressXP / requiredXP) * 100));
        const remaining = requiredXP - progressXP;
        
        // Update sidebar elements
        document.getElementById('progressPercent').textContent = `${Math.round(progressPercent)}%`;
        document.getElementById('xpRemaining').textContent = this.formatNumber(remaining);
        document.getElementById('nextLevel').textContent = level + 1;
        document.getElementById('sidebarLevel').textContent = level;
        document.getElementById('sidebarXP').textContent = this.formatNumber(xp);
        
        // Update progress ring
        const circle = document.querySelector('.progress-ring-circle');
        const circumference = 2 * Math.PI * 35; // radius = 35
        const offset = circumference - (progressPercent / 100) * circumference;
        circle.style.strokeDasharray = circumference;
        circle.style.strokeDashoffset = offset;
        circle.style.stroke = '#3b82f6';
        
        // Calculate efficiency (placeholder)
        const efficiency = Math.min(100, Math.round((this.playerData.stats?.ores_mined || 0) / Math.max(1, level) * 10));
        document.getElementById('sidebarEfficiency').textContent = `${efficiency}%`;
    }
    
    updateStatsTab() {
        const stats = this.playerData.stats || {};

        document.getElementById('oresMined').textContent = this.formatNumber(stats.ores_mined || 0);
        document.getElementById('timeMining').textContent = this.formatTime(stats.time_mined || 0);
        document.getElementById('moneyEarned').textContent = this.formatMoney(stats.money_earned || 0);
        document.getElementById('itemsSmelted').textContent = this.formatNumber(stats.items_smelted || 0);
    }

    updateShopTab() {
        // Load shop items and categories
        this.loadShopItems();
    }

    updatePawnTab() {
        this.requestData('inventory');
        this.updateSellCart();
    }

    updateSmeltingTab() {
        this.requestData('smeltJobs');
        this.loadRecipes();
    }

    loadShopItems() {
        const shopContainer = document.getElementById('shopItems');
        if (!shopContainer) return;

        const items = [
            { name: 'pickaxe_wood', label: 'Wooden Pickaxe', price: 100, category: 'tools', levelRequired: 1 },
            { name: 'pickaxe_stone', label: 'Stone Pickaxe', price: 250, category: 'tools', levelRequired: 5 },
            { name: 'pickaxe_iron', label: 'Iron Pickaxe', price: 500, category: 'tools', levelRequired: 10 },
            { name: 'pickaxe_steel', label: 'Steel Pickaxe', price: 1000, category: 'tools', levelRequired: 20 },
            { name: 'pickaxe_titanium', label: 'Titanium Pickaxe', price: 2500, category: 'tools', levelRequired: 30 },
            { name: 'pickaxe_diamond', label: 'Diamond Pickaxe', price: 5000, category: 'tools', levelRequired: 40 },
            { name: 'pickaxe_repairkit', label: 'Repair Kit', price: 50, category: 'supplies', levelRequired: 1 }
        ];

        shopContainer.innerHTML = '';

        items.forEach(item => {
            const canBuy = (this.playerData.level || 1) >= item.levelRequired;

            const itemElement = document.createElement('div');
            itemElement.className = `shop-item ${canBuy ? '' : 'disabled'}`;
            itemElement.innerHTML = `
                <div class="item-icon">
                    <i class="fas fa-hammer"></i>
                </div>
                <div class="item-info">
                    <div class="item-name">${item.label}</div>
                    <div class="item-price">${this.formatMoney(item.price)}</div>
                    <div class="item-level">Level ${item.levelRequired} required</div>
                </div>
                <button class="shop-buy-btn" ${canBuy ? '' : 'disabled'} onclick="purchaseItem('${item.name}', 1)">
                    Buy
                </button>
            `;
            shopContainer.appendChild(itemElement);
        });
    }
    
    calculateXPForLevel(level) {
        if (level <= 1) return 0;
        let totalXP = 0;
        const base = 100;
        const scale = 1.35;
        
        for (let i = 2; i <= level; i++) {
            totalXP += Math.floor(base * Math.pow(scale, i - 2));
        }
        return totalXP;
    }
    
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }
    
    formatTime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        
        if (hours > 0) {
            return `${hours}h ${minutes}m`;
        } else if (minutes > 0) {
            return `${minutes}m`;
        } else {
            return `${seconds}s`;
        }
    }
    
    formatMoney(amount) {
        return '$' + this.formatNumber(amount);
    }
    
    requestData(type = null) {
        if (type) {
            this.sendNUIMessage('requestData', { type });
        } else {
            // Request all data
            this.sendNUIMessage('requestData', { type: 'sellPrices' });
            this.sendNUIMessage('requestData', { type: 'smeltJobs' });
            this.sendNUIMessage('requestData', { type: 'leaderboards' });
        }
    }
    
    sendNUIMessage(action, data = {}) {
        fetch(`https://${GetParentResourceName()}/${action}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
    }
    
    showNotification(message, type = 'info', duration = 5000) {
        const container = document.getElementById('notifications');
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        
        const icons = {
            success: 'fas fa-check',
            error: 'fas fa-times',
            warning: 'fas fa-exclamation',
            info: 'fas fa-info'
        };
        
        notification.innerHTML = `
            <div class="notification-icon">
                <i class="${icons[type] || icons.info}"></i>
            </div>
            <div class="notification-content">${message}</div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        // Add close functionality
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.remove();
        });
        
        container.appendChild(notification);
        
        // Auto remove after duration
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, duration);
    }
    
    showXPGain(amount) {
        this.showNotification(`+${amount} XP`, 'success', 3000);
        this.updateUI();
    }
    
    showLevelUp(level) {
        this.showNotification(`Level Up! You are now level ${level}`, 'success', 5000);
        this.updateUI();
        
        // Play celebration effect
        this.playCelebrationEffect();
    }
    
    playCelebrationEffect() {
        // Add celebration animation class
        document.querySelector('.level-circle').classList.add('celebration');
        setTimeout(() => {
            document.querySelector('.level-circle').classList.remove('celebration');
        }, 2000);
    }
}

// Global functions for HTML onclick events
function closeMenu() {
    app.closeMenu();
}

function switchTab(tabName) {
    app.switchTab(tabName);
}

// Initialize app
const app = new MiningApp();

// Pawn Shop Functions
MiningApp.prototype.updatePawnTab = function() {
    this.requestData('inventory');
    this.updateSellCart();
}

function addToSellCart(item, quantity = 1) {
    const existingItem = app.sellCart.find(cartItem => cartItem.name === item.name);

    if (existingItem) {
        existingItem.quantity += quantity;
    } else {
        app.sellCart.push({
            name: item.name,
            label: item.label,
            quantity: quantity,
            price: app.sellPrices[item.name]?.currentPrice || 0,
            image: item.image
        });
    }

    app.updateSellCart();
}

function removeFromSellCart(itemName) {
    app.sellCart = app.sellCart.filter(item => item.name !== itemName);
    app.updateSellCart();
}

MiningApp.prototype.updateSellCart = function() {
    const cartContainer = document.getElementById('sellCart');
    const subtotalElement = document.getElementById('sellSubtotal');
    const taxElement = document.getElementById('sellTax');
    const totalElement = document.getElementById('sellTotal');
    const sellButton = document.getElementById('sellButton');

    if (this.sellCart.length === 0) {
        if (cartContainer) {
            cartContainer.innerHTML = `
                <div class="empty-cart">
                    <i class="fas fa-shopping-cart"></i>
                    <p>Add items to sell</p>
                </div>
            `;
        }
        if (subtotalElement) subtotalElement.textContent = '$0';
        if (taxElement) taxElement.textContent = '$0';
        if (totalElement) totalElement.textContent = '$0';
        if (sellButton) sellButton.disabled = true;
        return;
    }

    let subtotal = 0;
    if (cartContainer) cartContainer.innerHTML = '';

    this.sellCart.forEach(item => {
        const itemTotal = item.price * item.quantity;
        subtotal += itemTotal;

        if (cartContainer) {
            const cartItem = document.createElement('div');
            cartItem.className = 'cart-item';
            cartItem.innerHTML = `
                <div class="item-icon">
                    <i class="fas fa-gem"></i>
                </div>
                <div class="cart-item-info">
                    <div class="cart-item-name">${item.label}</div>
                    <div class="cart-item-details">${item.quantity}x @ ${this.formatMoney(item.price)}</div>
                </div>
                <div class="cart-item-price">${this.formatMoney(itemTotal)}</div>
                <button class="cart-item-remove" onclick="removeFromSellCart('${item.name}')">
                    <i class="fas fa-times"></i>
                </button>
            `;
            cartContainer.appendChild(cartItem);
        }
    });

    const tax = subtotal * 0.05; // 5% tax
    const total = subtotal - tax;

    if (subtotalElement) subtotalElement.textContent = this.formatMoney(subtotal);
    if (taxElement) taxElement.textContent = this.formatMoney(tax);
    if (totalElement) totalElement.textContent = this.formatMoney(total);
    if (sellButton) sellButton.disabled = false;
}

function sellItems() {
    if (app.sellCart.length === 0) return;

    const items = app.sellCart.map(item => ({
        item: item.name,
        quantity: item.quantity
    }));

    app.sendNUIMessage('sellItems', { items });
    app.sellCart = [];
    app.updateSellCart();
}

// Smelting Functions
MiningApp.prototype.updateSmeltingTab = function() {
    this.requestData('smeltJobs');
    this.loadRecipes();
}

MiningApp.prototype.loadRecipes = function() {
    // This would be populated with actual recipe data
    const recipes = [
        {
            id: 'copper_ingot',
            name: 'Copper Ingot',
            time: 30,
            inputs: [{ item: 'ore_copper', count: 5 }, { item: 'ore_coal', count: 5 }],
            outputs: [{ item: 'ingot_copper', count: 1 }],
            levelRequired: 1
        },
        {
            id: 'iron_ingot',
            name: 'Iron Ingot',
            time: 40,
            inputs: [{ item: 'ore_iron', count: 5 }, { item: 'ore_coal', count: 10 }],
            outputs: [{ item: 'ingot_iron', count: 1 }],
            levelRequired: 5
        }
    ];

    const recipeList = document.getElementById('recipeList');
    recipeList.innerHTML = '';

    recipes.forEach(recipe => {
        const canCraft = (this.playerData.level || 1) >= recipe.levelRequired;

        const recipeElement = document.createElement('div');
        recipeElement.className = `recipe-item ${canCraft ? '' : 'disabled'}`;
        recipeElement.innerHTML = `
            <div class="recipe-header">
                <div class="recipe-name">${recipe.name}</div>
                <div class="recipe-time">${recipe.time}s</div>
            </div>
            <div class="recipe-requirements">
                ${recipe.inputs.map(input => `
                    <div class="requirement-item">
                        <i class="fas fa-gem"></i>
                        ${input.count}x ${input.item}
                    </div>
                `).join('')}
            </div>
            <div class="recipe-outputs">
                ${recipe.outputs.map(output => `
                    <div class="output-item">
                        <i class="fas fa-arrow-right"></i>
                        ${output.count}x ${output.item}
                    </div>
                `).join('')}
            </div>
            <button class="recipe-start-btn" ${canCraft ? '' : 'disabled'} onclick="startSmelting('${recipe.id}', 1)">
                Start Smelting
            </button>
        `;
        recipeList.appendChild(recipeElement);
    });
}

function startSmelting(recipeId, quantity) {
    app.sendNUIMessage('startSmelting', { recipeId, quantity });
}

function cancelSmelting(jobId) {
    app.sendNUIMessage('cancelSmelting', { jobId });
}

function collectSmelt(jobId) {
    app.sendNUIMessage('collectSmelt', { jobId });
}

// Placement Functions
function openPlacement() {
    app.sendNUIMessage('openPlacement');
}

function closePlacement() {
    document.getElementById('placementUI').style.display = 'none';
    app.isPlacementMode = false;
    app.sendNUIMessage('closePlacement');
}

function updatePlacement() {
    const data = {
        rows: parseInt(document.getElementById('gridRows').value),
        cols: parseInt(document.getElementById('gridCols').value),
        spacing: parseFloat(document.getElementById('gridSpacing').value),
        yaw: parseFloat(document.getElementById('gridYaw').value),
        jitter: parseFloat(document.getElementById('gridJitter').value)
    };

    // Update display values
    document.getElementById('gridRowsValue').textContent = data.rows;
    document.getElementById('gridColsValue').textContent = data.cols;
    document.getElementById('gridSpacingValue').textContent = data.spacing.toFixed(1) + 'm';
    document.getElementById('gridYawValue').textContent = data.yaw.toFixed(0) + '°';
    document.getElementById('gridJitterValue').textContent = Math.round(data.jitter * 100) + '%';

    app.sendNUIMessage('updatePlacement', data);
}

function updateOreComposition() {
    const data = {
        oreComposition: {
            copper: parseFloat(document.getElementById('oreCopper').value),
            iron: parseFloat(document.getElementById('oreIron').value),
            silver: parseFloat(document.getElementById('oreSilver').value),
            gold: parseFloat(document.getElementById('oreGold').value)
        }
    };

    // Update display values
    document.getElementById('oreCopperValue').textContent = Math.round(data.oreComposition.copper * 100) + '%';
    document.getElementById('oreIronValue').textContent = Math.round(data.oreComposition.iron * 100) + '%';
    document.getElementById('oreSilverValue').textContent = Math.round(data.oreComposition.silver * 100) + '%';
    document.getElementById('oreGoldValue').textContent = Math.round(data.oreComposition.gold * 100) + '%';

    app.sendNUIMessage('updatePlacement', data);
}

function bakeGrid() {
    const name = document.getElementById('gridName').value || `Grid_${Date.now()}`;
    app.sendNUIMessage('bakeGrid', { name });
}

function clearPreview() {
    app.sendNUIMessage('clearPreview');
}

// Admin Functions
function reloadConfig() {
    app.sendNUIMessage('reloadConfig');
}

// Leaderboard Functions
function switchLeaderboard(type) {
    document.querySelectorAll('.lb-tab').forEach(tab => tab.classList.remove('active'));
    event.target.classList.add('active');

    // Update leaderboard display based on type
    app.updateLeaderboardDisplay(type);
}

// Shop Functions
function switchShopCategory(category) {
    document.querySelectorAll('.shop-cat').forEach(cat => cat.classList.remove('active'));
    event.target.classList.add('active');

    // Update shop display based on category
    app.updateShopDisplay(category);
}

function purchaseItem(itemName, quantity = 1) {
    app.sendNUIMessage('purchaseItem', { item: itemName, quantity });
}

// Helper function to get parent resource name
function GetParentResourceName() {
    return window.location.hostname;
}
