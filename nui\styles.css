/* ZG Mining System - QBOX Style UI */

:root {
    --primary-color: #3b82f6;
    --primary-dark: #1e40af;
    --secondary-color: #6366f1;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #06b6d4;
    
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-card: #1e293b;
    --bg-hover: #334155;
    
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #64748b;
    
    --border-color: #334155;
    --border-light: #475569;
    
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background: transparent;
    color: var(--text-primary);
    overflow: hidden;
    user-select: none;
}

.app-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

/* Main Menu */
.main-menu {
    width: 90vw;
    max-width: 1400px;
    height: 85vh;
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.menu-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem 2rem;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
}

.menu-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
}

.menu-title i {
    color: var(--primary-color);
}

.close-btn {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.5rem;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: var(--error-color);
    color: white;
    border-color: var(--error-color);
}

.menu-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Navigation Section */
.nav-section {
    width: 280px;
    background: var(--bg-secondary);
    border-right: 1px solid var(--border-color);
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    overflow-y: auto;
}

.nav-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all 0.2s ease;
}

.nav-card:hover {
    background: var(--bg-hover);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.nav-card.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.nav-card-icon {
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    font-size: 1.125rem;
}

.nav-card.active .nav-card-icon {
    background: rgba(255, 255, 255, 0.2);
}

.nav-card-content h3 {
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.nav-card-content p {
    font-size: 0.75rem;
    opacity: 0.8;
}

/* Content Section */
.content-section {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.content-header {
    margin-bottom: 2rem;
}

.content-header h2 {
    font-size: 1.875rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.content-header p {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Level Display */
.level-display {
    display: flex;
    gap: 2rem;
    align-items: flex-start;
}

.level-circle {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-lg);
    flex-shrink: 0;
}

.level-number {
    font-size: 2rem;
    font-weight: 700;
    color: white;
}

.level-label {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.8);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.xp-info {
    flex: 1;
}

.xp-bar-container {
    margin-bottom: 1.5rem;
}

.xp-bar {
    width: 100%;
    height: 0.75rem;
    background: var(--bg-tertiary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.xp-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--radius-lg);
    transition: width 0.3s ease;
}

.xp-text {
    font-size: 0.875rem;
    color: var(--text-secondary);
    text-align: center;
}

.level-perks h4 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--text-primary);
}

.perk-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.perk-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
}

.perk-icon {
    width: 1.5rem;
    height: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-color);
    color: white;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
}

.perk-text {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    transition: all 0.2s ease;
}

.stat-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-color);
    color: white;
    border-radius: var(--radius-lg);
    font-size: 1.25rem;
}

.stat-info {
    flex: 1;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* Sidebar Section */
.sidebar-section {
    width: 300px;
    background: var(--bg-secondary);
    border-left: 1px solid var(--border-color);
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    overflow-y: auto;
}

.sidebar-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
}

.sidebar-card h3 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.next-level-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.level-progress-circle {
    position: relative;
    width: 80px;
    height: 80px;
}

.progress-ring {
    transform: rotate(-90deg);
}

.progress-ring-circle {
    fill: transparent;
    stroke: var(--bg-tertiary);
    stroke-width: 6;
    stroke-dasharray: 220;
    stroke-dashoffset: 220;
    transition: stroke-dashoffset 0.3s ease;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
}

.progress-details {
    text-align: center;
}

.progress-details p {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
}

.quick-stats {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.quick-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
}

.quick-stat .stat-label {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.quick-stat .stat-value {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* Leaderboards */
.leaderboard-container {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.leaderboard-tabs {
    display: flex;
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
}

.lb-tab {
    flex: 1;
    padding: 1rem;
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    font-weight: 500;
}

.lb-tab:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.lb-tab.active {
    background: var(--primary-color);
    color: white;
}

.leaderboard-list {
    padding: 1.5rem;
}

.leaderboard-entry {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.2s ease;
}

.leaderboard-entry:last-child {
    border-bottom: none;
}

.leaderboard-entry:hover {
    background: var(--bg-hover);
}

.lb-rank {
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    font-weight: 600;
    font-size: 0.875rem;
}

.lb-rank.gold {
    background: #fbbf24;
}

.lb-rank.silver {
    background: #9ca3af;
}

.lb-rank.bronze {
    background: #d97706;
}

.lb-player {
    flex: 1;
    font-weight: 500;
    color: var(--text-primary);
}

.lb-score {
    font-weight: 600;
    color: var(--primary-color);
}

/* Shop */
.shop-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.shop-categories {
    display: flex;
    gap: 0.5rem;
}

.shop-cat {
    padding: 0.75rem 1.5rem;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    font-weight: 500;
}

.shop-cat:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.shop-cat.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.shop-items {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.shop-item {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    transition: all 0.2s ease;
}

.shop-item:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.shop-item-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.shop-item-icon {
    width: 3rem;
    height: 3rem;
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: var(--primary-color);
}

.shop-item-info h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.shop-item-info p {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.shop-item-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.shop-stat {
    display: flex;
    justify-content: space-between;
    font-size: 0.75rem;
}

.shop-stat-label {
    color: var(--text-secondary);
}

.shop-stat-value {
    color: var(--text-primary);
    font-weight: 500;
}

.shop-item-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.shop-price {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--success-color);
}

.shop-buy-btn {
    padding: 0.5rem 1rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    font-weight: 500;
}

.shop-buy-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

.shop-buy-btn:disabled {
    background: var(--bg-tertiary);
    color: var(--text-muted);
    cursor: not-allowed;
    transform: none;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-menu {
        width: 95vw;
        height: 90vh;
    }

    .nav-section {
        width: 240px;
    }

    .sidebar-section {
        width: 260px;
    }
}

@media (max-width: 768px) {
    .menu-content {
        flex-direction: column;
    }

    .nav-section {
        width: 100%;
        height: auto;
        border-right: none;
        border-bottom: 1px solid var(--border-color);
        flex-direction: row;
        overflow-x: auto;
        padding: 1rem;
    }

    .nav-card {
        min-width: 200px;
    }

    .sidebar-section {
        width: 100%;
        border-left: none;
        border-top: 1px solid var(--border-color);
    }

    .level-display {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .shop-items {
        grid-template-columns: 1fr;
    }
}

/* Pawn Shop */
.pawn-container {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 2rem;
    height: 500px;
}

.inventory-section,
.sell-section {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
}

.inventory-section h3,
.sell-section h3 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.inventory-list {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.inventory-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
}

.inventory-item:hover {
    background: var(--bg-hover);
    border-color: var(--primary-color);
}

.inventory-item.selected {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.item-icon {
    width: 2.5rem;
    height: 2.5rem;
    background: var(--bg-secondary);
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: var(--primary-color);
}

.item-info {
    flex: 1;
}

.item-name {
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.item-count {
    font-size: 0.75rem;
    opacity: 0.8;
}

.item-price {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--success-color);
}

.sell-cart {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 1rem;
}

.empty-cart {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--text-muted);
    text-align: center;
}

.empty-cart i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.cart-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    margin-bottom: 0.5rem;
}

.cart-item-info {
    flex: 1;
}

.cart-item-name {
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.cart-item-details {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.cart-item-remove {
    background: var(--error-color);
    color: white;
    border: none;
    border-radius: var(--radius-sm);
    width: 1.5rem;
    height: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 0.75rem;
}

.sell-total {
    border-top: 1px solid var(--border-color);
    padding-top: 1rem;
    margin-bottom: 1rem;
}

.total-line {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.total-line.total {
    font-weight: 700;
    font-size: 1rem;
    color: var(--text-primary);
    border-top: 1px solid var(--border-color);
    padding-top: 0.5rem;
    margin-top: 0.5rem;
}

.sell-btn {
    width: 100%;
    padding: 0.75rem;
    background: var(--success-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.sell-btn:hover:not(:disabled) {
    background: #059669;
    transform: translateY(-1px);
}

.sell-btn:disabled {
    background: var(--bg-tertiary);
    color: var(--text-muted);
    cursor: not-allowed;
    transform: none;
}

/* Smelting */
.smelting-container {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 2rem;
    height: 500px;
}

.recipes-section,
.queue-section {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
}

.recipe-list,
.queue-list {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.recipe-item {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.recipe-item:hover {
    background: var(--bg-hover);
    border-color: var(--primary-color);
}

.recipe-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.75rem;
}

.recipe-name {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
}

.recipe-time {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.recipe-requirements {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.requirement-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    background: var(--bg-secondary);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.recipe-outputs {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.output-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    background: var(--success-color);
    color: white;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
}

.recipe-start-btn {
    width: 100%;
    padding: 0.5rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.75rem;
    font-weight: 500;
}

.recipe-start-btn:hover:not(:disabled) {
    background: var(--primary-dark);
}

.recipe-start-btn:disabled {
    background: var(--bg-tertiary);
    color: var(--text-muted);
    cursor: not-allowed;
}

.empty-queue {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--text-muted);
    text-align: center;
}

.empty-queue i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.queue-item {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 1rem;
}

.queue-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.75rem;
}

.queue-recipe-name {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
}

.queue-status {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-sm);
    font-weight: 500;
}

.queue-status.active {
    background: var(--warning-color);
    color: white;
}

.queue-status.completed {
    background: var(--success-color);
    color: white;
}

.queue-progress {
    margin-bottom: 0.75rem;
}

.progress-bar {
    width: 100%;
    height: 0.5rem;
    background: var(--bg-secondary);
    border-radius: var(--radius-sm);
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: var(--primary-color);
    border-radius: var(--radius-sm);
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 0.75rem;
    color: var(--text-secondary);
    text-align: center;
}

.queue-actions {
    display: flex;
    gap: 0.5rem;
}

.queue-btn {
    flex: 1;
    padding: 0.5rem;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.75rem;
    font-weight: 500;
}

.queue-collect-btn {
    background: var(--success-color);
    color: white;
}

.queue-collect-btn:hover {
    background: #059669;
}

.queue-cancel-btn {
    background: var(--error-color);
    color: white;
}

.queue-cancel-btn:hover {
    background: #dc2626;
}

/* Placement UI */
.placement-ui {
    position: fixed;
    top: 2rem;
    right: 2rem;
    width: 350px;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    z-index: 1000;
}

.placement-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.placement-header h2 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
}

.placement-controls {
    padding: 1.5rem;
    max-height: 70vh;
    overflow-y: auto;
}

.control-group {
    margin-bottom: 1.5rem;
}

.control-group label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.control-row {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.control-row input[type="range"] {
    flex: 1;
}

.control-row span {
    font-size: 0.875rem;
    color: var(--text-secondary);
    min-width: 3rem;
    text-align: center;
}

input[type="range"] {
    width: 100%;
    height: 0.5rem;
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    outline: none;
    -webkit-appearance: none;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 1rem;
    height: 1rem;
    background: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
}

input[type="range"]::-moz-range-thumb {
    width: 1rem;
    height: 1rem;
    background: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

input[type="text"] {
    width: 100%;
    padding: 0.75rem;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

input[type="text"]:focus {
    outline: none;
    border-color: var(--primary-color);
}

.ore-composition h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.ore-sliders {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.ore-slider {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.ore-slider label {
    min-width: 4rem;
    font-size: 0.75rem;
    margin-bottom: 0;
}

.ore-slider span {
    min-width: 2.5rem;
    font-size: 0.75rem;
    text-align: right;
}

.placement-actions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.bake-btn,
.clear-btn {
    padding: 0.75rem;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.bake-btn {
    background: var(--success-color);
    color: white;
}

.bake-btn:hover {
    background: #059669;
    transform: translateY(-1px);
}

.clear-btn {
    background: var(--error-color);
    color: white;
}

.clear-btn:hover {
    background: #dc2626;
    transform: translateY(-1px);
}

/* Admin Panel */
.admin-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.admin-section {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
}

.admin-section h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.admin-btn {
    width: 100%;
    padding: 0.75rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.admin-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

.admin-btn:last-child {
    margin-bottom: 0;
}

/* Notifications */
.notifications-container {
    position: fixed;
    top: 2rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 2000;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    pointer-events: none;
}

.notification {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 1rem 1.5rem;
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    min-width: 300px;
    max-width: 500px;
    pointer-events: auto;
    animation: slideInDown 0.3s ease;
}

.notification.success {
    border-color: var(--success-color);
}

.notification.error {
    border-color: var(--error-color);
}

.notification.warning {
    border-color: var(--warning-color);
}

.notification.info {
    border-color: var(--info-color);
}

.notification-icon {
    width: 1.5rem;
    height: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 0.875rem;
    color: white;
}

.notification.success .notification-icon {
    background: var(--success-color);
}

.notification.error .notification-icon {
    background: var(--error-color);
}

.notification.warning .notification-icon {
    background: var(--warning-color);
}

.notification.info .notification-icon {
    background: var(--info-color);
}

.notification-content {
    flex: 1;
    font-size: 0.875rem;
    color: var(--text-primary);
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
}

.notification-close:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3000;
}

.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    color: var(--text-primary);
}

.loading-spinner i {
    font-size: 2rem;
    color: var(--primary-color);
}

.loading-spinner p {
    font-size: 1rem;
    font-weight: 500;
}

/* Animations */
@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-1rem);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 0.5rem;
}

::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
    background: var(--border-light);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.fade-in {
    animation: fadeIn 0.3s ease;
}

.fade-out {
    animation: fadeOut 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Level up celebration animation */
.level-circle.celebration {
    animation: celebrate 2s ease-in-out;
}

@keyframes celebrate {
    0% { transform: scale(1); }
    25% { transform: scale(1.1); box-shadow: 0 0 20px var(--primary-color); }
    50% { transform: scale(1.05); }
    75% { transform: scale(1.1); box-shadow: 0 0 30px var(--secondary-color); }
    100% { transform: scale(1); }
}

/* XP gain animation */
.xp-gain-effect {
    position: absolute;
    top: -2rem;
    right: 1rem;
    color: var(--success-color);
    font-weight: 700;
    font-size: 1.125rem;
    animation: xpGain 2s ease-out forwards;
    pointer-events: none;
}

@keyframes xpGain {
    0% {
        opacity: 1;
        transform: translateY(0);
    }
    100% {
        opacity: 0;
        transform: translateY(-2rem);
    }
}

/* Pulse animation for active elements */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Glow effect for important elements */
.glow {
    box-shadow: 0 0 10px var(--primary-color);
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { box-shadow: 0 0 10px var(--primary-color); }
    to { box-shadow: 0 0 20px var(--primary-color), 0 0 30px var(--primary-color); }
}

/* Loading spinner animation */
.spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Shake animation for errors */
.shake {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Bounce animation for success */
.bounce {
    animation: bounce 0.6s ease-in-out;
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

/* Progress bar fill animation */
.progress-fill-animated {
    animation: progressFill 1s ease-out;
}

@keyframes progressFill {
    from { width: 0%; }
}

/* Hover animations */
.hover-lift {
    transition: transform 0.2s ease;
}

.hover-lift:hover {
    transform: translateY(-2px);
}

.hover-glow {
    transition: box-shadow 0.2s ease;
}

.hover-glow:hover {
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

/* Mobile responsive animations */
@media (max-width: 768px) {
    .level-circle.celebration {
        animation: celebrateMobile 1.5s ease-in-out;
    }

    @keyframes celebrateMobile {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .hover-lift:hover {
        transform: none; /* Disable on mobile */
    }
}

/* Accessibility - Reduced motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .level-circle.celebration,
    .xp-gain-effect,
    .pulse,
    .glow,
    .spinner,
    .shake,
    .bounce {
        animation: none !important;
    }
}
