-- ZG Mining System - Target System Integration

local PedTargets = {}
local RockTargets = {}

-- Initialize target system
CreateThread(function()
    Wait(2000) -- Wait for bridges to initialize
    
    SetupPedTargets()
    Utils.Debug('Target system initialized')
end)

-- Setup PED targets
function SetupPedTargets()
    for pedType, pedData in pairs(Config.Peds) do
        local ped = _G.MiningPeds and _G.MiningPeds[pedType]
        if ped and DoesEntityExist(ped) then
            SetupPedTarget(ped, pedType, pedData)
        end
    end
end

-- Setup individual PED target
function SetupPedTarget(ped, pedType, pedData)
    local options = GetPedTargetOptions(pedType)
    
    if Config.Target.system ~= 'text' then
        PedTargets[pedType] = Bridges.Target.AddTargetEntity(ped, options)
    end
    
    Utils.Debug('Setup target for %s PED', pedType)
end

-- Get target options for PED type
function GetPedTargetOptions(pedType)
    local options = {}
    
    if pedType == 'foreman' then
        table.insert(options, {
            type = 'client',
            event = 'ZGMining:TalkToForeman',
            icon = 'fas fa-hammer',
            label = Utils.Locale('talk_to_foreman'),
            canInteract = function()
                return true
            end
        })
        
        table.insert(options, {
            type = 'client',
            event = 'ZGMining:OpenShop',
            icon = 'fas fa-shopping-cart',
            label = Utils.Locale('the_shop'),
            canInteract = function()
                return true
            end
        })
        
        table.insert(options, {
            type = 'client',
            event = 'ZGMining:RepairPickaxe',
            icon = 'fas fa-wrench',
            label = Utils.Locale('repair_pickaxe'),
            canInteract = function()
                return HasDamagedPickaxe()
            end
        })
        
    elseif pedType == 'seller' then
        table.insert(options, {
            type = 'client',
            event = 'ZGMining:TalkToSeller',
            icon = 'fas fa-coins',
            label = Utils.Locale('talk_to_seller'),
            canInteract = function()
                return true
            end
        })
        
        table.insert(options, {
            type = 'client',
            event = 'ZGMining:OpenPawnShop',
            icon = 'fas fa-store',
            label = Utils.Locale('pawn_shop'),
            canInteract = function()
                return HasSellableItems()
            end
        })
        
    elseif pedType == 'smelter' then
        table.insert(options, {
            type = 'client',
            event = 'ZGMining:TalkToSmelter',
            icon = 'fas fa-fire',
            label = Utils.Locale('talk_to_smelter'),
            canInteract = function()
                return true
            end
        })
        
        table.insert(options, {
            type = 'client',
            event = 'ZGMining:OpenSmelting',
            icon = 'fas fa-industry',
            label = Utils.Locale('smelting'),
            canInteract = function()
                return HasSmeltableItems()
            end
        })
    end
    
    return options
end

-- Check if player has damaged pickaxe
function HasDamagedPickaxe()
    -- This would integrate with inventory system
    -- For now, return true as example
    return true
end

-- Check if player has sellable items
function HasSellableItems()
    -- This would check inventory for ores/ingots
    return true
end

-- Check if player has smeltable items
function HasSmeltableItems()
    -- This would check inventory for ores
    return true
end

-- PED Interaction Events
RegisterNetEvent('ZGMining:TalkToForeman', function()
    local messages = {
        Utils.Locale('foreman_greeting'),
        'Need a new pickaxe? I\'ve got the best tools in the business!',
        'Your pickaxe looking a bit worn? I can fix that right up.',
        'Mining is hard work, but the rewards are worth it!'
    }
    
    local message = messages[math.random(#messages)]
    Framework.Notify(message, 'info', 5000)
    
    -- Play PED animation
    local ped = _G.MiningPeds.foreman
    if ped and DoesEntityExist(ped) then
        TaskPlayAnim(ped, 'mp_common', 'givetake1_a', 8.0, -8.0, 2000, 0, 0, false, false, false)
    end
end)

RegisterNetEvent('ZGMining:TalkToSeller', function()
    local messages = {
        Utils.Locale('seller_greeting'),
        'Got some quality ores there! I\'ll give you a fair price.',
        'The market\'s looking good today - perfect time to sell!',
        'I buy all kinds of ores and ingots. What have you got?'
    }
    
    local message = messages[math.random(#messages)]
    Framework.Notify(message, 'info', 5000)
    
    local ped = _G.MiningPeds.seller
    if ped and DoesEntityExist(ped) then
        TaskPlayAnim(ped, 'mp_common', 'givetake1_a', 8.0, -8.0, 2000, 0, 0, false, false, false)
    end
end)

RegisterNetEvent('ZGMining:TalkToSmelter', function()
    local messages = {
        Utils.Locale('smelter_greeting'),
        'Fire up the furnace! What are we smelting today?',
        'Raw ores are good, but refined ingots are where the real money is!',
        'I can turn your rough ores into beautiful ingots.'
    }
    
    local message = messages[math.random(#messages)]
    Framework.Notify(message, 'info', 5000)
    
    local ped = _G.MiningPeds.smelter
    if ped and DoesEntityExist(ped) then
        TaskPlayAnim(ped, 'mp_common', 'givetake1_a', 8.0, -8.0, 2000, 0, 0, false, false, false)
    end
end)

-- Shop and menu events
RegisterNetEvent('ZGMining:OpenShop', function()
    OpenMiningMenu()
    
    -- Switch to shop tab
    SendNUIMessage({
        action = 'switchTab',
        tab = 'shop'
    })
end)

RegisterNetEvent('ZGMining:OpenPawnShop', function()
    OpenMiningMenu()
    
    -- Switch to pawn shop tab
    SendNUIMessage({
        action = 'switchTab',
        tab = 'pawn'
    })
end)

RegisterNetEvent('ZGMining:OpenSmelting', function()
    OpenMiningMenu()
    
    -- Switch to smelting tab
    SendNUIMessage({
        action = 'switchTab',
        tab = 'smelting'
    })
end)

RegisterNetEvent('ZGMining:RepairPickaxe', function()
    -- Open repair dialog
    SendNUIMessage({
        action = 'openRepairDialog',
        data = {
            pickaxes = GetDamagedPickaxes(),
            repairCosts = GetRepairCosts()
        }
    })
end)

-- Get damaged pickaxes from inventory
function GetDamagedPickaxes()
    local damaged = {}
    
    -- This would integrate with inventory system
    -- For now, return sample data
    for itemName, itemData in pairs(Items.Tools) do
        if string.find(itemName, 'pickaxe_') and itemName ~= 'pickaxe_repairkit' then
            table.insert(damaged, {
                name = itemName,
                label = itemData.label,
                currentDurability = math.random(10, 40),
                maxDurability = itemData.durabilityMax,
                tier = itemData.tier
            })
        end
    end
    
    return damaged
end

-- Get repair costs
function GetRepairCosts()
    local costs = {}
    
    for itemName, itemData in pairs(Items.Tools) do
        if string.find(itemName, 'pickaxe_') and itemName ~= 'pickaxe_repairkit' then
            costs[itemName] = {
                cash = math.floor(itemData.price * 0.2), -- 20% of purchase price
                repairKit = 1
            }
        end
    end
    
    return costs
end

-- Text-based interaction system (fallback)
CreateThread(function()
    if Config.Target.system ~= 'text' then return end
    
    while true do
        Wait(0)
        
        local playerCoords = GetEntityCoords(PlayerPedId())
        local nearPed = nil
        local pedType = nil
        
        -- Check distance to each PED
        for pType, pedData in pairs(Config.Peds) do
            local distance = Utils.GetDistance(playerCoords, pedData.coords.xyz)
            if distance <= Config.Target.distance then
                nearPed = _G.MiningPeds and _G.MiningPeds[pType]
                pedType = pType
                break
            end
        end
        
        if nearPed and DoesEntityExist(nearPed) then
            -- Show interaction text
            local label = ''
            if pedType == 'foreman' then
                label = Utils.Locale('talk_to_foreman')
            elseif pedType == 'seller' then
                label = Utils.Locale('talk_to_seller')
            elseif pedType == 'smelter' then
                label = Utils.Locale('talk_to_smelter')
            end
            
            SetTextComponentFormat('STRING')
            AddTextComponentString(string.format('~INPUT_CONTEXT~ %s', label))
            DisplayHelpTextFromStringLabel(0, 0, 1, -1)
            
            if IsControlJustPressed(0, Config.Controls.interact) then
                if pedType == 'foreman' then
                    TriggerEvent('ZGMining:TalkToForeman')
                elseif pedType == 'seller' then
                    TriggerEvent('ZGMining:TalkToSeller')
                elseif pedType == 'smelter' then
                    TriggerEvent('ZGMining:TalkToSmelter')
                end
            end
        end
    end
end)

-- Cleanup targets on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        -- Remove all PED targets
        for pedType, targetId in pairs(PedTargets) do
            local ped = _G.MiningPeds and _G.MiningPeds[pedType]
            if ped and DoesEntityExist(ped) then
                Bridges.Target.RemoveTargetEntity(ped)
            end
        end
        
        PedTargets = {}
    end
end)
