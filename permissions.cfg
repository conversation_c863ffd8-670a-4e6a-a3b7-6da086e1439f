# ZG Mining System - ACE Permissions Configuration
# Add these lines to your server.cfg or permissions configuration

# Admin permissions for mining system
add_ace group.admin mining.admin allow
add_ace group.admin mining.place allow
add_ace group.admin mining.manage allow
add_ace group.admin mining.reload allow
add_ace group.admin mining.give allow

# Moderator permissions (optional)
add_ace group.mod mining.place allow
add_ace group.mod mining.manage allow

# Job-based permissions (optional)
# Uncomment and modify as needed for your server

# Mining job permissions
# add_ace job.miner mining.bonus allow
# add_ace job.miner mining.advanced allow

# VIP permissions (optional)
# add_ace group.vip mining.bonus allow
# add_ace group.vip mining.priority allow

# Individual player permissions (examples)
# add_ace identifier.license:abc123 mining.admin allow
# add_ace identifier.steam:110000123456789 mining.place allow

# Permission inheritance
add_principal group.admin group.mod
add_principal group.mod group.user

# Custom permission groups for mining
add_ace group.mining_admin mining.admin allow
add_ace group.mining_admin mining.place allow
add_ace group.mining_admin mining.manage allow
add_ace group.mining_admin mining.reload allow
add_ace group.mining_admin mining.give allow

add_ace group.mining_mod mining.place allow
add_ace group.mining_mod mining.manage allow

# Add players to mining groups (examples)
# add_principal identifier.license:abc123 group.mining_admin
# add_principal identifier.steam:110000123456789 group.mining_mod

# Permission descriptions:
# mining.admin     - Full administrative access to mining system
# mining.place     - Can place and manage mining grids
# mining.manage    - Can manage existing grids and view admin panel
# mining.reload    - Can reload mining configuration
# mining.give      - Can give mining items to players
# mining.bonus     - Receives job/VIP bonuses
# mining.priority  - Priority in smelting queues
# mining.advanced  - Access to advanced features
