Bridges = {}

-- Inventory Bridge Detection and Setup
Bridges.Inventory = {}

function Bridges.SetupInventory()
    if Config.Inventory.system == 'auto' then
        if GetResourceState('ox_inventory') == 'started' then
            Config.Inventory.system = 'ox_inventory'
        elseif GetResourceState('qb-inventory') == 'started' then
            Config.Inventory.system = 'qb-inventory'
        elseif GetResourceState('qs-inventory') == 'started' then
            Config.Inventory.system = 'qs-inventory'
        else
            Config.Inventory.system = 'default'
        end
    end
    
    Utils.Debug('Inventory system detected: %s', Config.Inventory.system)
end

-- Target System Bridge Detection and Setup
Bridges.Target = {}

function Bridges.SetupTarget()
    if Config.Target.system == 'auto' then
        if GetResourceState('ox_target') == 'started' then
            Config.Target.system = 'ox_target'
        elseif GetResourceState('qb-target') == 'started' then
            Config.Target.system = 'qb-target'
        elseif GetResourceState('qtarget') == 'started' then
            Config.Target.system = 'qtarget'
        else
            Config.Target.system = 'text'
        end
    end
    
    Utils.Debug('Target system detected: %s', Config.Target.system)
end

-- Target System Functions
function Bridges.Target.AddBoxZone(name, coords, size, options, targetOptions)
    if Config.Target.system == 'ox_target' then
        return exports.ox_target:addBoxZone({
            coords = coords,
            size = size,
            rotation = options.heading or 0,
            debug = Config.Debug,
            options = targetOptions
        })
    elseif Config.Target.system == 'qb-target' then
        return exports['qb-target']:AddBoxZone(name, coords, size.x, size.y, {
            name = name,
            heading = options.heading or 0,
            debugPoly = Config.Debug,
            minZ = coords.z - size.z/2,
            maxZ = coords.z + size.z/2,
        }, {
            options = targetOptions,
            distance = Config.Target.distance
        })
    elseif Config.Target.system == 'qtarget' then
        return exports.qtarget:AddBoxZone(name, coords, size.x, size.y, {
            name = name,
            heading = options.heading or 0,
            debugPoly = Config.Debug,
            minZ = coords.z - size.z/2,
            maxZ = coords.z + size.z/2,
        }, {
            options = targetOptions,
            distance = Config.Target.distance
        })
    end
    return nil
end

function Bridges.Target.AddTargetEntity(entity, options)
    if Config.Target.system == 'ox_target' then
        return exports.ox_target:addLocalEntity(entity, options)
    elseif Config.Target.system == 'qb-target' then
        return exports['qb-target']:AddTargetEntity(entity, {
            options = options,
            distance = Config.Target.distance
        })
    elseif Config.Target.system == 'qtarget' then
        return exports.qtarget:AddTargetEntity(entity, {
            options = options,
            distance = Config.Target.distance
        })
    end
    return nil
end

function Bridges.Target.RemoveZone(name)
    if Config.Target.system == 'ox_target' then
        exports.ox_target:removeZone(name)
    elseif Config.Target.system == 'qb-target' then
        exports['qb-target']:RemoveZone(name)
    elseif Config.Target.system == 'qtarget' then
        exports.qtarget:RemoveZone(name)
    end
end

function Bridges.Target.RemoveTargetEntity(entity)
    if Config.Target.system == 'ox_target' then
        exports.ox_target:removeLocalEntity(entity)
    elseif Config.Target.system == 'qb-target' then
        exports['qb-target']:RemoveTargetEntity(entity)
    elseif Config.Target.system == 'qtarget' then
        exports.qtarget:RemoveTargetEntity(entity)
    end
end

-- Inventory Functions (Client-side)
if IsDuplicityVersion() == 0 then -- Client only
    function Bridges.Inventory.OpenInventory(type, name, data)
        if Config.Inventory.system == 'ox_inventory' then
            return exports.ox_inventory:openInventory(type, name, data)
        elseif Config.Inventory.system == 'qb-inventory' then
            TriggerServerEvent('inventory:server:OpenInventory', type, name, data)
        elseif Config.Inventory.system == 'qs-inventory' then
            TriggerServerEvent('qs-inventory:server:OpenInventory', type, name, data)
        end
    end
end

-- Progress Bar Functions (Client-side)
if IsDuplicityVersion() == 0 then -- Client only
    Bridges.Progress = {}
    
    function Bridges.Progress.Start(data, cb)
        if GetResourceState('progressbar') == 'started' then
            exports['progressbar']:Progress({
                name = data.name or 'mining_action',
                duration = data.duration,
                label = data.label,
                useWhileDead = false,
                canCancel = data.canCancel or true,
                controlDisables = {
                    disableMovement = true,
                    disableCarMovement = true,
                    disableMouse = false,
                    disableCombat = true,
                },
                animation = data.animation,
                prop = data.prop,
            }, function(cancelled)
                if cb then cb(not cancelled) end
            end)
        elseif GetResourceState('qb-core') == 'started' then
            QBCore.Functions.Progressbar(data.name or 'mining_action', data.label, data.duration, false, data.canCancel or true, {
                disableMovement = true,
                disableCarMovement = true,
                disableMouse = false,
                disableCombat = true,
            }, data.animation or {}, data.prop or {}, {}, function()
                if cb then cb(true) end
            end, function()
                if cb then cb(false) end
            end)
        else
            -- Fallback progress
            local startTime = GetGameTimer()
            CreateThread(function()
                while GetGameTimer() - startTime < data.duration do
                    Wait(100)
                    if IsControlJustPressed(0, 177) and (data.canCancel or true) then -- BACKSPACE
                        if cb then cb(false) end
                        return
                    end
                end
                if cb then cb(true) end
            end)
        end
    end
end

-- Notification Functions
Bridges.Notify = {}

function Bridges.Notify.Send(source, message, type, duration)
    if IsDuplicityVersion() == 1 then -- Server
        TriggerClientEvent('ZGMining:Notify', source, message, type, duration)
    else -- Client
        if GetResourceState('qb-core') == 'started' then
            QBCore.Functions.Notify(message, type, duration)
        elseif GetResourceState('es_extended') == 'started' then
            ESX.ShowNotification(message)
        elseif GetResourceState('ox_lib') == 'started' then
            exports.ox_lib:notify({
                title = 'Mining System',
                description = message,
                type = type or 'info',
                duration = duration or 5000
            })
        else
            -- Fallback notification
            SetNotificationTextEntry('STRING')
            AddTextComponentString(message)
            DrawNotification(false, false)
        end
    end
end

-- Client-side notification handler
if IsDuplicityVersion() == 0 then
    RegisterNetEvent('ZGMining:Notify', function(message, type, duration)
        Bridges.Notify.Send(nil, message, type, duration)
    end)
end

-- Skill Check Functions (Client-side)
if IsDuplicityVersion() == 0 then
    Bridges.SkillCheck = {}
    
    function Bridges.SkillCheck.Start(difficulty, cb)
        if GetResourceState('qb-skillbar') == 'started' then
            exports['qb-skillbar']:GetSkillbarObject().Start({
                duration = math.random(2000, 4000),
                pos = math.random(10, 30),
                width = math.random(10, 20),
            }, function()
                if cb then cb(true) end
            end, function()
                if cb then cb(false) end
            end)
        elseif GetResourceState('ox_lib') == 'started' then
            local success = exports.ox_lib:skillCheck(difficulty or {'easy', 'easy'})
            if cb then cb(success) end
        else
            -- Simple fallback skill check
            local success = math.random() > 0.3 -- 70% success rate
            if cb then cb(success) end
        end
    end
end

-- Initialize bridges
CreateThread(function()
    Wait(1000) -- Wait for resources to load
    Bridges.SetupInventory()
    Bridges.SetupTarget()
end)
