-- ZG Mining System - Rock Management

local SpawnedRocks = {}
local RockEntities = {}
local RockTargets = {}

-- Initialize rock system
CreateThread(function()
    while not NetworkIsPlayerActive(PlayerId()) do
        Wait(100)
    end
    
    -- Request initial rock data
    TriggerServerEvent('ZGMining:RequestRockData')
end)

-- Spawn rock entity
function SpawnRock(rockData)
    local coords = rockData.coords
    local model = GetRockModel(rockData.oreType)
    
    RequestModel(model)
    while not HasModelLoaded(model) do
        Wait(100)
    end
    
    local rock = CreateObject(model, coords.x, coords.y, coords.z, false, false, false)
    SetEntityAsMissionEntity(rock, true, true)
    FreezeEntityPosition(rock, true)
    
    -- Apply scale if specified
    if rockData.scale and rockData.scale ~= 1.0 then
        SetObjectScale(rock, rockData.scale)
    end
    
    -- Store rock entity
    RockEntities[rockData.id] = rock
    SpawnedRocks[rockData.id] = rockData
    
    -- Setup target interaction
    SetupRockTarget(rock, rockData)
    
    Utils.Debug('Spawned rock %s at %s', rockData.id, coords)
    
    return rock
end

-- Remove rock entity
function RemoveRock(rockId)
    local entity = RockEntities[rockId]
    if entity and DoesEntityExist(entity) then
        -- Remove target
        if RockTargets[rockId] then
            Bridges.Target.RemoveTargetEntity(entity)
            RockTargets[rockId] = nil
        end
        
        DeleteObject(entity)
        RockEntities[rockId] = nil
    end
    
    SpawnedRocks[rockId] = nil
end

-- Get rock model based on ore type
function GetRockModel(oreType)
    local models = {
        copper = `prop_rock_1_a`,
        iron = `prop_rock_1_b`,
        silver = `prop_rock_1_c`,
        gold = `prop_rock_1_d`,
        titanium = `prop_rock_1_e`,
        diamond = `prop_rock_1_f`
    }
    
    return models[oreType] or `prop_rock_1_a`
end

-- Setup target interaction for rock
function SetupRockTarget(entity, rockData)
    if Config.Target.system == 'text' then
        -- Text-based interaction handled in main.lua
        return
    end
    
    local options = {
        {
            type = 'client',
            event = 'ZGMining:InteractRock',
            icon = 'fas fa-pickaxe',
            label = Utils.Locale('start_mining'),
            rockId = rockData.id,
            canInteract = function()
                return not rockData.depleted and not CurrentMining
            end
        }
    }
    
    if Config.Target.system == 'ox_target' then
        RockTargets[rockData.id] = Bridges.Target.AddTargetEntity(entity, options)
    else
        RockTargets[rockData.id] = Bridges.Target.AddTargetEntity(entity, options)
    end
end

-- Update rock health display
function UpdateRockHealth(rockId, health, maxHealth)
    local rockData = SpawnedRocks[rockId]
    if not rockData then return end
    
    rockData.health = health
    rockData.maxHealth = maxHealth
    
    -- Update visual state based on health
    local entity = RockEntities[rockId]
    if entity and DoesEntityExist(entity) then
        local healthPercent = health / maxHealth
        
        -- Change rock appearance based on health
        if healthPercent <= 0.25 then
            -- Very damaged - add scorch marks
            SetEntityRenderScorched(entity, true)
        elseif healthPercent <= 0.5 then
            -- Moderately damaged - reduce alpha slightly
            SetEntityAlpha(entity, 200, false)
        end
    end
end

-- Mark rock as depleted
function DepleteRock(rockId)
    local rockData = SpawnedRocks[rockId]
    if not rockData then return end
    
    rockData.depleted = true
    
    local entity = RockEntities[rockId]
    if entity and DoesEntityExist(entity) then
        -- Make rock appear depleted
        SetEntityAlpha(entity, 100, false)
        SetEntityRenderScorched(entity, true)
        
        -- Remove target interaction
        if RockTargets[rockId] then
            Bridges.Target.RemoveTargetEntity(entity)
            RockTargets[rockId] = nil
        end
        
        -- Add particle effect
        CreateDepletedEffect(GetEntityCoords(entity))
    end
end

-- Respawn rock
function RespawnRock(rockId, newRockData)
    local entity = RockEntities[rockId]
    if entity and DoesEntityExist(entity) then
        -- Reset visual state
        SetEntityAlpha(entity, 255, false)
        SetEntityRenderScorched(entity, false)
        
        -- Update rock data
        SpawnedRocks[rockId] = newRockData
        
        -- Re-setup target
        SetupRockTarget(entity, newRockData)
        
        -- Add respawn effect
        CreateRespawnEffect(GetEntityCoords(entity))
    end
end

-- Create depleted effect
function CreateDepletedEffect(coords)
    RequestNamedPtfxAsset('core')
    while not HasNamedPtfxAssetLoaded('core') do
        Wait(100)
    end
    
    UseParticleFxAssetNextCall('core')
    StartParticleFxNonLoopedAtCoord('ent_dst_rocks', coords.x, coords.y, coords.z, 0.0, 0.0, 0.0, 1.0, false, false, false)
end

-- Create respawn effect
function CreateRespawnEffect(coords)
    RequestNamedPtfxAsset('core')
    while not HasNamedPtfxAssetLoaded('core') do
        Wait(100)
    end
    
    UseParticleFxAssetNextCall('core')
    StartParticleFxNonLoopedAtCoord('ent_sht_steam', coords.x, coords.y, coords.z, 0.0, 0.0, 0.0, 1.0, false, false, false)
end

-- Create mining hit effect
function CreateMiningHitEffect(coords, success)
    RequestNamedPtfxAsset('core')
    while not HasNamedPtfxAssetLoaded('core') do
        Wait(100)
    end
    
    UseParticleFxAssetNextCall('core')
    if success then
        StartParticleFxNonLoopedAtCoord('ent_dst_concrete_large', coords.x, coords.y, coords.z, 0.0, 0.0, 0.0, 0.5, false, false, false)
    else
        StartParticleFxNonLoopedAtCoord('ent_dst_dust_settle', coords.x, coords.y, coords.z, 0.0, 0.0, 0.0, 0.3, false, false, false)
    end
end

-- Rock culling system for performance
CreateThread(function()
    while true do
        Wait(5000) -- Check every 5 seconds
        
        local playerCoords = GetEntityCoords(PlayerPedId())
        local cullDistance = Config.Performance.cullDistance
        
        for rockId, rockData in pairs(SpawnedRocks) do
            local distance = Utils.GetDistance(playerCoords, rockData.coords)
            local entity = RockEntities[rockId]
            
            if distance > cullDistance then
                -- Cull rock if too far
                if entity and DoesEntityExist(entity) then
                    RemoveRock(rockId)
                end
            elseif distance <= cullDistance and not entity then
                -- Spawn rock if close enough and not spawned
                SpawnRock(rockData)
            end
        end
    end
end)

-- Event Handlers
RegisterNetEvent('ZGMining:RockData', function(rocks)
    -- Clear existing rocks
    for rockId, _ in pairs(SpawnedRocks) do
        RemoveRock(rockId)
    end
    
    -- Spawn new rocks
    for rockId, rockData in pairs(rocks) do
        local distance = Utils.GetDistance(GetEntityCoords(PlayerPedId()), rockData.coords)
        if distance <= Config.Performance.cullDistance then
            SpawnRock(rockData)
        else
            -- Store data without spawning entity
            SpawnedRocks[rockId] = rockData
        end
    end
    
    Utils.Debug('Loaded %d rocks', Utils.TableLength(rocks))
end)

RegisterNetEvent('ZGMining:RockHealthUpdate', function(rockId, health, maxHealth)
    UpdateRockHealth(rockId, health, maxHealth)
end)

RegisterNetEvent('ZGMining:RockDepleted', function(rockId)
    DepleteRock(rockId)
end)

RegisterNetEvent('ZGMining:RockRespawned', function(gridId, rockId, rockData)
    RespawnRock(rockId, rockData)
end)

RegisterNetEvent('ZGMining:MiningHitEffect', function(rockId, coords, success)
    CreateMiningHitEffect(coords, success)
    
    -- Play sound
    local soundName = success and 'PICK_UP_WEAPON' or 'ERROR'
    PlaySoundFrontend(-1, soundName, 'HUD_FRONTEND_DEFAULT_SOUNDSET', false)
end)

-- Rock interaction event
RegisterNetEvent('ZGMining:InteractRock', function(data)
    if data.rockId then
        StartMining(data.rockId)
    end
end)

-- Cleanup on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        -- Clean up all spawned rocks
        for rockId, _ in pairs(SpawnedRocks) do
            RemoveRock(rockId)
        end
    end
end)
