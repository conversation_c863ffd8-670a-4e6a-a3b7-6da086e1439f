-- ZG Mining System - Server Main

-- Player Data Storage
local PlayerData = {}
local MiningGrids = {}
local ActiveMiners = {}

-- Commands
RegisterCommand('miningmenu', function(source, args)
    TriggerClientEvent('ZGMining:OpenMenu', source)
end, false)

RegisterCommand('miningplace', function(source, args)
    if not Framework.HasPermission(source, Config.Placement.permissions.place) then
        Framework.Notify(source, Utils.Locale('permission_denied'), 'error')
        return
    end

    TriggerClientEvent('ZGMining:OpenPlacement', source)
end, false)

RegisterCommand('miningreload', function(source, args)
    if not Framework.HasPermission(source, 'mining.admin') then
        Framework.Notify(source, Utils.Locale('permission_denied'), 'error')
        return
    end

    -- Reload configs (this would need to be implemented)
    Framework.Notify(source, Utils.Locale('config_reloaded'), 'success')
end, false)

RegisterCommand('mininggive', function(source, args)
    if not Framework.HasPermission(source, 'mining.admin') then
        Framework.Notify(source, Utils.Locale('permission_denied'), 'error')
        return
    end

    local targetId = tonumber(args[1])
    local itemName = args[2]
    local count = tonumber(args[3]) or 1

    if not targetId or not itemName then
        Framework.Notify(source, Utils.Locale('command_usage', '/mininggive [id] [item] [count]'), 'error')
        return
    end

    if not Items.All[itemName] then
        Framework.Notify(source, Utils.Locale('error_invalid_item'), 'error')
        return
    end

    if Framework.AddItem(targetId, itemName, count) then
        Framework.Notify(source, string.format('Gave %s x%d to player %d', itemName, count, targetId), 'success')
        Framework.Notify(targetId, Utils.Locale('item_received', Items.All[itemName].label, count), 'success')
    else
        Framework.Notify(source, 'Failed to give item', 'error')
    end
end, false)

-- Database Tables
local function CreateTables()
    MySQL.query([[
        CREATE TABLE IF NOT EXISTS `players_mining` (
            `identifier` VARCHAR(50) NOT NULL,
            `xp` INT(11) NOT NULL DEFAULT 0,
            `level` INT(11) NOT NULL DEFAULT 1,
            `ores_mined` INT(11) NOT NULL DEFAULT 0,
            `time_mined` INT(11) NOT NULL DEFAULT 0,
            `money_earned` INT(11) NOT NULL DEFAULT 0,
            `items_smelted` INT(11) NOT NULL DEFAULT 0,
            `pickaxes_broken` INT(11) NOT NULL DEFAULT 0,
            `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`identifier`),
            INDEX `idx_level` (`level`),
            INDEX `idx_xp` (`xp`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ]])
    
    MySQL.query([[
        CREATE TABLE IF NOT EXISTS `mining_grids` (
            `id` INT(11) NOT NULL AUTO_INCREMENT,
            `name` VARCHAR(100) NOT NULL,
            `data` LONGTEXT NOT NULL,
            `created_by` VARCHAR(50) NOT NULL,
            `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            `active` TINYINT(1) DEFAULT 1,
            PRIMARY KEY (`id`),
            INDEX `idx_active` (`active`),
            INDEX `idx_created_by` (`created_by`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ]])
    
    Utils.Log('info', 'Database tables created/verified')
end

-- Player Data Management
local function LoadPlayerData(source)
    local identifier = Framework.GetPlayerIdentifier(source)
    if not identifier then return end
    
    MySQL.single('SELECT * FROM players_mining WHERE identifier = ?', {identifier}, function(result)
        if result then
            PlayerData[source] = {
                identifier = identifier,
                xp = result.xp,
                level = result.level,
                stats = {
                    ores_mined = result.ores_mined,
                    time_mined = result.time_mined,
                    money_earned = result.money_earned,
                    items_smelted = result.items_smelted,
                    pickaxes_broken = result.pickaxes_broken
                }
            }
        else
            -- Create new player data
            PlayerData[source] = {
                identifier = identifier,
                xp = 0,
                level = 1,
                stats = {
                    ores_mined = 0,
                    time_mined = 0,
                    money_earned = 0,
                    items_smelted = 0,
                    pickaxes_broken = 0
                }
            }
            
            MySQL.insert('INSERT INTO players_mining (identifier) VALUES (?)', {identifier})
        end
        
        TriggerClientEvent('ZGMining:PlayerDataLoaded', source, PlayerData[source])
    end)
end

local function SavePlayerData(source)
    local data = PlayerData[source]
    if not data then return end
    
    MySQL.update([[
        UPDATE players_mining SET 
        xp = ?, level = ?, ores_mined = ?, time_mined = ?, 
        money_earned = ?, items_smelted = ?, pickaxes_broken = ?
        WHERE identifier = ?
    ]], {
        data.xp, data.level, data.stats.ores_mined, data.stats.time_mined,
        data.stats.money_earned, data.stats.items_smelted, data.stats.pickaxes_broken,
        data.identifier
    })
end

-- XP and Leveling
local function AddXP(source, amount)
    local data = PlayerData[source]
    if not data then return end
    
    data.xp = data.xp + amount
    local newLevel, remainingXP = Utils.CalculateLevelFromXP(data.xp)
    
    if newLevel > data.level then
        data.level = newLevel
        TriggerClientEvent('ZGMining:LevelUp', source, newLevel)
        Framework.Notify(source, Utils.Locale('level_up', newLevel), 'success')
        
        -- Trigger level up event
        TriggerEvent('ZGMining:PlayerLevelUp', source, newLevel, data.level - 1)
    end
    
    TriggerClientEvent('ZGMining:XPGained', source, amount, data.xp, data.level)
    TriggerClientEvent('ZGMining:UpdatePlayerData', source, data)
    
    SavePlayerData(source)
end

-- Mining System
RegisterNetEvent('ZGMining:StartMining', function(rockId, pickaxeData)
    local source = source
    local playerData = PlayerData[source]
    
    if not playerData then
        Framework.Notify(source, Utils.Locale('error_generic'), 'error')
        return
    end
    
    -- Validate pickaxe
    if not pickaxeData or not Items.All[pickaxeData.name] then
        Framework.Notify(source, Utils.Locale('error_invalid_item'), 'error')
        return
    end
    
    -- Check if player already mining
    if ActiveMiners[source] then
        Framework.Notify(source, Utils.Locale('mining_in_progress'), 'warning')
        return
    end
    
    -- Validate rock exists and is available
    local rock = MiningGrids.rocks and MiningGrids.rocks[rockId]
    if not rock or rock.depleted then
        Framework.Notify(source, Utils.Locale('rock_depleted'), 'warning')
        return
    end
    
    -- Start mining session
    ActiveMiners[source] = {
        rockId = rockId,
        pickaxe = pickaxeData,
        startTime = os.time(),
        hits = 0
    }
    
    TriggerClientEvent('ZGMining:MiningStarted', source, rockId, pickaxeData)
end)

RegisterNetEvent('ZGMining:MiningHit', function(rockId, skillCheckSuccess)
    local source = source
    local miner = ActiveMiners[source]
    local playerData = PlayerData[source]
    
    if not miner or not playerData or miner.rockId ~= rockId then
        return
    end
    
    local rock = MiningGrids.rocks and MiningGrids.rocks[rockId]
    if not rock or rock.depleted then
        TriggerClientEvent('ZGMining:MiningFailed', source, 'rock_depleted')
        ActiveMiners[source] = nil
        return
    end
    
    local pickaxeItem = Items.All[miner.pickaxe.name]
    if not pickaxeItem then
        TriggerClientEvent('ZGMining:MiningFailed', source, 'invalid_pickaxe')
        ActiveMiners[source] = nil
        return
    end
    
    -- Calculate damage and durability loss
    local damage = skillCheckSuccess and pickaxeItem.damage or (pickaxeItem.damage * 0.5)
    local isCritFail = Utils.ShouldCritFail(pickaxeItem.tier)
    local durabilityLoss = Utils.CalculateDurabilityLoss(pickaxeItem.tier, skillCheckSuccess, isCritFail)
    
    -- Update rock health
    rock.health = rock.health - damage
    miner.hits = miner.hits + 1
    
    -- Update pickaxe durability
    miner.pickaxe.durability = math.max(0, miner.pickaxe.durability - durabilityLoss)
    
    -- Check if pickaxe broke
    if miner.pickaxe.durability <= 0 then
        Framework.RemoveItem(source, miner.pickaxe.name, 1)
        Framework.Notify(source, Utils.Locale('pickaxe_broken'), 'error')
        TriggerClientEvent('ZGMining:PickaxeBroken', source)
        
        playerData.stats.pickaxes_broken = playerData.stats.pickaxes_broken + 1
        ActiveMiners[source] = nil
        return
    end
    
    -- Give small XP for hit
    if skillCheckSuccess then
        AddXP(source, math.random(1, 3))
    end
    
    -- Check if rock is depleted
    if rock.health <= 0 then
        -- Rock depleted, give rewards
        local oreType = rock.oreType or 'copper'
        local oreItem = 'ore_' .. oreType
        local dropAmount = math.random(Config.Rocks.dropRates.ore.min, Config.Rocks.dropRates.ore.max)
        
        -- Level bonus for double drops
        local doubleChance = math.floor(playerData.level / 5) * Config.XP.perks.doubleDropChance
        if math.random() < doubleChance then
            dropAmount = dropAmount * 2
            Framework.Notify(source, 'Double drop!', 'success')
        end
        
        -- Add ore to inventory
        if Framework.AddItem(source, oreItem, dropAmount) then
            Framework.Notify(source, Utils.Locale('item_received', Items.All[oreItem].label, dropAmount), 'success')
        end
        
        -- Rare gem chance
        local gemChance = Config.Rocks.dropRates.gem.min + 
                         (Config.Rocks.dropRates.gem.max - Config.Rocks.dropRates.gem.min) * (playerData.level / Config.XP.maxLevel)
        
        if math.random() < gemChance then
            local gemItem = 'ore_diamondShard'
            if Framework.AddItem(source, gemItem, 1) then
                Framework.Notify(source, Utils.Locale('item_received', Items.All[gemItem].label, 1), 'success')
            end
        end
        
        -- Update stats
        playerData.stats.ores_mined = playerData.stats.ores_mined + dropAmount
        
        -- Give completion XP
        local completionXP = math.random(10, 25) + math.floor(playerData.level * 0.5)
        AddXP(source, completionXP)
        
        -- Mark rock as depleted and schedule respawn
        rock.depleted = true
        rock.respawnTime = os.time() + Config.Rocks.respawnTime
        
        TriggerClientEvent('ZGMining:MiningComplete', source, rockId, {
            ore = oreItem,
            amount = dropAmount,
            xp = completionXP
        })
        
        -- Trigger mining complete event
        TriggerEvent('ZGMining:MiningComplete', source, rockId, oreType, dropAmount)
        
        ActiveMiners[source] = nil
    else
        -- Continue mining
        TriggerClientEvent('ZGMining:MiningHit', source, rock.health, rock.maxHealth or Config.Rocks.health[rock.oreType])
    end
    
    SavePlayerData(source)
end)

RegisterNetEvent('ZGMining:StopMining', function()
    local source = source
    ActiveMiners[source] = nil
    TriggerClientEvent('ZGMining:MiningStopped', source)
end)

-- Grid Management
local function LoadMiningGrids()
    MySQL.query('SELECT * FROM mining_grids WHERE active = 1', {}, function(results)
        if results then
            for _, grid in pairs(results) do
                local gridData = json.decode(grid.data)
                if gridData then
                    MiningGrids[grid.id] = gridData
                    MiningGrids[grid.id].id = grid.id
                    MiningGrids[grid.id].name = grid.name
                    
                    -- Initialize rocks if not present
                    if not MiningGrids[grid.id].rocks then
                        MiningGrids[grid.id].rocks = {}
                    end
                end
            end
            
            Utils.Log('info', 'Loaded %d mining grids', #results)
            TriggerClientEvent('ZGMining:GridsLoaded', -1, MiningGrids)
        end
    end)
end

-- Rock Respawn System
CreateThread(function()
    while true do
        Wait(30000) -- Check every 30 seconds
        
        local currentTime = os.time()
        for gridId, grid in pairs(MiningGrids) do
            if grid.rocks then
                for rockId, rock in pairs(grid.rocks) do
                    if rock.depleted and rock.respawnTime and currentTime >= rock.respawnTime then
                        -- Respawn rock
                        rock.depleted = false
                        rock.health = Config.Rocks.health[rock.oreType] or 100
                        rock.respawnTime = nil
                        
                        TriggerClientEvent('ZGMining:RockRespawned', -1, gridId, rockId, rock)
                    end
                end
            end
        end
    end
end)

-- Events
AddEventHandler('playerConnecting', function()
    local source = source
    LoadPlayerData(source)
end)

AddEventHandler('playerDropped', function()
    local source = source
    if PlayerData[source] then
        SavePlayerData(source)
        PlayerData[source] = nil
    end
    
    if ActiveMiners[source] then
        ActiveMiners[source] = nil
    end
end)

-- Resource start/stop
AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        CreateTables()
        LoadMiningGrids()
        
        -- Register items
        for itemName, itemData in pairs(Items.All) do
            Framework.RegisterItem(itemName, itemData)
        end
    end
end)

AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        -- Save all player data
        for source, _ in pairs(PlayerData) do
            SavePlayerData(source)
        end
    end
end)

-- Grid Management Events
RegisterNetEvent('ZGMining:BakeGrid', function(gridData)
    local source = source

    if not Framework.HasPermission(source, Config.Placement.permissions.place) then
        Framework.Notify(source, Utils.Locale('permission_denied'), 'error')
        return
    end

    -- Validate grid data
    if not gridData or not gridData.positions or #gridData.positions == 0 then
        TriggerClientEvent('ZGMining:GridBaked', source, false, 'Invalid grid data')
        return
    end

    -- Create rocks data
    local rocks = {}
    for i, pos in pairs(gridData.positions) do
        local rockId = string.format('%s_%d', gridData.name, i)
        rocks[rockId] = {
            id = rockId,
            coords = pos.coords,
            oreType = pos.oreType,
            health = Config.Rocks.health[pos.oreType] or 100,
            maxHealth = Config.Rocks.health[pos.oreType] or 100,
            depleted = false,
            respawnTime = nil
        }
    end

    -- Save to database
    MySQL.insert('INSERT INTO mining_grids (name, data, created_by) VALUES (?, ?, ?)', {
        gridData.name,
        json.encode({
            center = gridData.center,
            settings = gridData.settings,
            rocks = rocks
        }),
        Framework.GetPlayerIdentifier(source)
    }, function(gridId)
        if gridId then
            -- Add to runtime grids
            MiningGrids[gridId] = {
                id = gridId,
                name = gridData.name,
                center = gridData.center,
                settings = gridData.settings,
                rocks = rocks
            }

            -- Notify all clients
            TriggerClientEvent('ZGMining:GridsLoaded', -1, MiningGrids)
            TriggerClientEvent('ZGMining:GridBaked', source, true, 'Grid placed successfully')

            Utils.Log('info', 'Player %s created mining grid %s with %d rocks',
                Framework.GetPlayerIdentifier(source), gridData.name, #gridData.positions)
        else
            TriggerClientEvent('ZGMining:GridBaked', source, false, 'Database error')
        end
    end)
end)

-- Player data requests
RegisterNetEvent('ZGMining:RequestPlayerData', function()
    local source = source
    LoadPlayerData(source)
end)

RegisterNetEvent('ZGMining:RequestRockData', function()
    local source = source
    local allRocks = {}

    for gridId, grid in pairs(MiningGrids) do
        if grid.rocks then
            for rockId, rock in pairs(grid.rocks) do
                allRocks[rockId] = rock
            end
        end
    end

    TriggerClientEvent('ZGMining:RockData', source, allRocks)
end)

-- Leaderboards
RegisterNetEvent('ZGMining:GetLeaderboards', function()
    local source = source

    -- Get top miners
    MySQL.query('SELECT identifier, ores_mined, level, money_earned FROM players_mining ORDER BY ores_mined DESC LIMIT 10', {}, function(topMiners)
        -- Get top earners
        MySQL.query('SELECT identifier, money_earned, level, ores_mined FROM players_mining ORDER BY money_earned DESC LIMIT 10', {}, function(topEarners)
            -- Get highest levels
            MySQL.query('SELECT identifier, level, xp, ores_mined FROM players_mining ORDER BY level DESC, xp DESC LIMIT 10', {}, function(topLevels)

                local leaderboards = {
                    miners = topMiners or {},
                    earners = topEarners or {},
                    levels = topLevels or {}
                }

                TriggerClientEvent('ZGMining:ReceiveLeaderboards', source, leaderboards)
            end)
        end)
    end)
end)

-- Purchase system
RegisterNetEvent('ZGMining:PurchaseItem', function(itemName, quantity)
    local source = source
    local playerData = PlayerData[source]

    if not playerData then
        Framework.Notify(source, Utils.Locale('error_generic'), 'error')
        return
    end

    local item = Items.All[itemName]
    if not item or not item.price then
        Framework.Notify(source, Utils.Locale('error_invalid_item'), 'error')
        return
    end

    -- Check level requirement
    if item.levelRequired and playerData.level < item.levelRequired then
        Framework.Notify(source, Utils.Locale('level_requirement', item.levelRequired), 'error')
        return
    end

    local totalCost = item.price * quantity

    -- Check if player has enough money
    if Framework.GetMoney(source, 'cash') < totalCost then
        Framework.Notify(source, Utils.Locale('insufficient_money'), 'error')
        return
    end

    -- Process purchase
    if Framework.RemoveMoney(source, 'cash', totalCost, 'Mining shop purchase') then
        if Framework.AddItem(source, itemName, quantity) then
            Framework.Notify(source, Utils.Locale('purchase_successful'), 'success')
            Utils.Log('info', 'Player %s purchased %s x%d for $%d',
                Framework.GetPlayerIdentifier(source), itemName, quantity, totalCost)
        else
            -- Refund if item add failed
            Framework.AddMoney(source, 'cash', totalCost, 'Mining shop refund')
            Framework.Notify(source, Utils.Locale('error_generic'), 'error')
        end
    else
        Framework.Notify(source, Utils.Locale('error_generic'), 'error')
    end
end)

-- Exports
exports('GetPlayerMiningData', function(source)
    return PlayerData[source]
end)

exports('AddMiningXP', function(source, amount)
    AddXP(source, amount)
end)

exports('GetMiningGrids', function()
    return MiningGrids
end)

exports('RegisterOreType', function(oreData)
    if oreData.name and not Config.Rocks.health[oreData.name] then
        Config.Rocks.health[oreData.name] = oreData.health or 100
        Utils.Log('info', 'Registered custom ore type: %s', oreData.name)
        return true
    end
    return false
end)
