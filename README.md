# ZG Mining System

A comprehensive, production-grade mining system for FiveM servers with advanced features, 3D placement tools, and cross-framework compatibility.

## Features

### 🎯 Core Features
- **Multi-Framework Support**: Auto-detects and works with QBCore, ESX, and QBOX
- **3D Grid Placement**: Advanced admin tools with live preview and ghosting
- **Progressive Mining System**: 6 pickaxe tiers with durability and repair mechanics
- **XP & Leveling**: 50 levels with perks and bonuses
- **Smelting System**: Queue-based processing with server-side persistence
- **Dynamic Economy**: Supply/demand pricing with market fluctuations
- **Target Integration**: Supports ox_target, qb-target, qtarget with text fallback

### 🎨 User Interface
- **QBOX-Style Design**: Modern, dark theme with smooth animations
- **Responsive Layout**: Works on all screen sizes
- **Keyboard & Controller Support**: Full navigation support
- **Real-time Updates**: Live progress tracking and notifications

### ⚡ Performance
- **Optimized**: <0.01ms idle, <0.20ms active per player
- **Scalable**: Handles 50+ concurrent miners
- **Efficient**: Smart culling and pooling systems
- **Zero Restarts**: Hot-reloadable configuration

### 🔧 Admin Tools
- **3D Placement Mode**: Visual grid placement with real-time preview
- **Grid Management**: Save, load, and modify mining grids
- **Live Configuration**: Adjust settings without restarts
- **Comprehensive Logging**: Detailed activity tracking

## Installation

See [INSTALL.md](INSTALL.md) for detailed installation instructions.

## Configuration

### Basic Setup
```lua
Config.Framework = 'auto' -- 'auto', 'qbcore', 'esx', 'qbox'
Config.Target.system = 'auto' -- 'auto', 'ox_target', 'qb-target', 'qtarget', 'text'
Config.Inventory.system = 'auto' -- 'auto', 'ox_inventory', 'qb-inventory', 'qs-inventory'
```

### PED Locations
The system includes three NPCs at the specified coordinates:
- **Mining Foreman**: `2944.4258, 2746.8281, 43.3645, 288.2603`
- **Ore Buyer**: `1090.5807, -1999.9025, 30.9295, 136.3622`
- **Smelter**: `1109.9464, -2008.1166, 31.0586, 58.9880`

### XP System
```lua
Config.XP = {
    base = 100,
    scale = 1.35,
    maxLevel = 50,
    perks = {
        sellBonus = 0.05, -- +5% per 10 levels
        doubleDropChance = 0.02, -- +2% per 5 levels
        smeltTimeReduction = 0.05 -- -5% per 8 levels
    }
}
```

## Commands

| Command | Description | Permission |
|---------|-------------|------------|
| `/miningmenu` | Open mining menu | All players |
| `/miningplace` | Open 3D placement mode | `mining.admin` |
| `/miningreload` | Reload configuration | `mining.admin` |
| `/mininggive [id] [item] [count]` | Give mining items | `mining.admin` |

## Items

### Pickaxes
- `pickaxe_wood` - Basic wooden pickaxe (Level 1)
- `pickaxe_stone` - Stone pickaxe (Level 5)
- `pickaxe_iron` - Iron pickaxe (Level 10)
- `pickaxe_steel` - Steel pickaxe (Level 20)
- `pickaxe_titanium` - Titanium pickaxe (Level 30)
- `pickaxe_diamond` - Diamond pickaxe (Level 40)
- `pickaxe_repairkit` - Repair kit for pickaxes

### Ores
- `ore_coal` - Coal ore (fuel)
- `ore_copper` - Copper ore
- `ore_iron` - Iron ore
- `ore_silver` - Silver ore
- `ore_gold` - Gold ore
- `ore_titanium` - Titanium ore
- `ore_diamondShard` - Diamond shard

### Ingots
- `ingot_copper` - Refined copper
- `ingot_iron` - Refined iron
- `ingot_silver` - Refined silver
- `ingot_gold` - Refined gold
- `ingot_titanium` - Refined titanium
- `gem_diamond` - Cut diamond
- `coal_coke` - High-grade fuel

## Events

### Client Events
```lua
-- Mining events
TriggerEvent('ZGMining:MiningStarted', rockId, pickaxeData)
TriggerEvent('ZGMining:MiningComplete', rockId, rewards)
TriggerEvent('ZGMining:LevelUp', newLevel)

-- UI events
TriggerEvent('ZGMining:OpenMenu')
TriggerEvent('ZGMining:OpenPlacement')
```

### Server Events
```lua
-- Player events
TriggerEvent('ZGMining:PlayerLevelUp', source, newLevel, oldLevel)
TriggerEvent('ZGMining:MiningComplete', source, rockId, oreType, amount)
TriggerEvent('ZGMining:ItemsSold', source, items, totalValue)
TriggerEvent('ZGMining:SmeltComplete', source, recipeId, quantity, jobId)
```

## Exports

### Client Exports
```lua
-- Open mining menu
exports['ZGMining']:OpenMiningMenu()

-- Open placement UI (admin only)
exports['ZGMining']:OpenPlacementUI()

-- Get player mining data
local level = exports['ZGMining']:GetMiningLevel()
local xp = exports['ZGMining']:GetMiningXP()
```

### Server Exports
```lua
-- Add custom smelt recipe
exports['ZGMining']:AddSmeltRecipe({
    id = 'custom_recipe',
    label = 'Custom Recipe',
    inputs = {{item = 'ore_copper', count = 5}},
    outputs = {{item = 'ingot_copper', count = 1}},
    time = 30,
    xp = 25,
    levelRequired = 1
})

-- Register custom ore type
exports['ZGMining']:RegisterOreType({
    name = 'custom_ore',
    health = 100,
    dropRates = {min = 3, max = 7}
})

-- Add sell price modifier
exports['ZGMining']:AddSellModifier(function(source, item, basePrice)
    -- Custom price calculation
    return basePrice * 1.1
end)

-- Get player mining data
local playerData = exports['ZGMining']:GetPlayerMiningData(source)

-- Add XP to player
exports['ZGMining']:AddMiningXP(source, 50)
```

## Database Tables

The system automatically creates the following tables:

### players_mining
Stores player mining progress and statistics.

### mining_grids
Stores placed mining grids and their configurations.

### mining_smelt_jobs
Tracks active and completed smelting jobs.

### mining_price_history
Records price history for dynamic pricing system.

## Performance Optimization

- **Idle Performance**: ≤0.01ms per frame
- **Active Mining**: ≤0.20ms per player
- **Rock Culling**: Automatic distance-based culling
- **Smart Pooling**: Efficient entity management
- **Batch Updates**: Reduced network traffic

## Troubleshooting

### Common Issues

1. **Items not showing in inventory**
   - Ensure items are registered in your framework's item configuration
   - Check the INSTALL.md for framework-specific setup

2. **Target system not working**
   - Verify target resource is started before ZG Mining
   - Check Config.Target.system setting

3. **Database errors**
   - Ensure oxmysql is installed and configured
   - Check database permissions

4. **Performance issues**
   - Adjust Config.Performance settings
   - Reduce Config.Rocks.maxConcurrent

### Debug Mode
Enable debug mode in config.lua:
```lua
Config.Debug = true
```

## Support

For support, bug reports, or feature requests:
- Create an issue on GitHub
- Join our Discord server
- Check the documentation

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Credits

- **ZG Development** - Main development
- **Community Contributors** - Testing and feedback
- **Framework Teams** - QBCore, ESX, QBOX support

---

**Version**: 1.0.0  
**Last Updated**: 2024-01-01  
**Compatibility**: FiveM, QBCore, ESX, QBOX
