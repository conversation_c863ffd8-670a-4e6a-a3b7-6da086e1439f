# Mining System 3D Models

This directory contains 3D models and textures used by the mining system.

## Directory Structure

```
assets/
├── models/
│   ├── pickaxes/          # Pickaxe models for different tiers
│   │   ├── pickaxe_wood.ydr
│   │   ├── pickaxe_wood.ytd
│   │   ├── pickaxe_stone.ydr
│   │   ├── pickaxe_stone.ytd
│   │   └── ...
│   └── rocks/             # Rock models for different ore types
│       ├── rock_copper.ydr
│       ├── rock_copper.ytd
│       └── ...
└── props/
    ├── ghost_rock.yft     # Ghost prop for placement preview
    ├── ghost_rock.ytd
    ├── furnace.yft        # Smelting furnace model
    └── furnace.ytd
```

## Model Requirements

### Pickaxe Models
- **Format**: .ydr (drawable) + .ytd (texture dictionary)
- **Poly Count**: 500-1500 triangles (optimize for performance)
- **Textures**: 512x512 or 1024x1024 maximum
- **Attachment**: Must be compatible with weapon attachment bones

### Rock Models
- **Format**: .ydr (drawable) + .ytd (texture dictionary)
- **Poly Count**: 200-800 triangles per rock
- **Variations**: Different models for each ore type
- **Collision**: Simple collision mesh for interaction

### Ghost Props
- **Format**: .yft (fragment) + .ytd (texture dictionary)
- **Purpose**: Preview props for 3D placement system
- **Appearance**: Semi-transparent, blue-tinted
- **Performance**: Very low poly count (under 200 triangles)

## Model Guidelines

### Performance Optimization
- Keep polygon counts low for better performance
- Use efficient UV mapping to minimize texture size
- Implement LOD (Level of Detail) models when possible
- Optimize collision meshes separately from visual meshes

### Visual Consistency
- Maintain consistent art style across all models
- Use similar lighting and material properties
- Ensure models scale appropriately in-game
- Test models in various lighting conditions

### Technical Requirements
- Models must be compatible with GTA V's rendering engine
- Use standard GTA V material types and shaders
- Ensure proper normal mapping for detail
- Test collision detection thoroughly

## Creating Custom Models

### Tools Required
- **3D Modeling Software**: Blender, 3ds Max, or Maya
- **GTA V Modding Tools**: OpenIV, CodeWalker
- **Texture Software**: Photoshop, GIMP, or Substance Painter

### Workflow
1. **Model Creation**: Create base geometry in 3D software
2. **UV Mapping**: Unwrap UVs efficiently
3. **Texturing**: Create diffuse, normal, and specular maps
4. **Export**: Convert to GTA V format using modding tools
5. **Testing**: Test in-game for performance and appearance

### Texture Guidelines
- **Diffuse Maps**: Main color and detail
- **Normal Maps**: Surface detail and depth
- **Specular Maps**: Reflectivity and material properties
- **Resolution**: 512x512 for small props, 1024x1024 for detailed items

## Installation

1. Place model files (.ydr, .yft) in appropriate directories
2. Place texture files (.ytd) alongside model files
3. Update fxmanifest.lua to include new files
4. Restart resource to load new models
5. Test all models in-game

## Default Models

If custom models are not available, the system will fall back to:
- **Pickaxes**: Standard GTA V tool props
- **Rocks**: Various GTA V rock props
- **Ghost Props**: Simple primitive shapes

## Troubleshooting

### Common Issues
- **Model not loading**: Check file paths and fxmanifest.lua
- **Texture missing**: Ensure .ytd files are present and named correctly
- **Performance issues**: Reduce polygon count or texture resolution
- **Collision problems**: Verify collision mesh is properly configured

### Testing Checklist
- [ ] Models load without errors
- [ ] Textures display correctly
- [ ] Collision detection works
- [ ] Performance impact is acceptable
- [ ] Models scale appropriately
- [ ] LOD transitions are smooth (if applicable)

## Legal Considerations

When using custom models:
- Ensure you have rights to use all assets
- Credit original creators when required
- Avoid using copyrighted content without permission
- Consider licensing requirements for commercial servers

## Contributing

If you create models for the mining system:
1. Follow the guidelines above
2. Test thoroughly in various scenarios
3. Provide both high and low-poly versions
4. Include proper documentation
5. Consider sharing with the community

---

**Note**: This directory contains placeholder information. Actual model files need to be created or sourced separately.
